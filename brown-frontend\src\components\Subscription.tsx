import { useState } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Check, Star, Mail, Users } from "lucide-react";

interface SubscriptionTier {
  id: string;
  name: string;
  description: string;
  price: string;
  billingPeriod: string;
  features: string[];
  icon: React.ComponentType<{ className?: string }>;
  popular?: boolean;
}

const subscriptionTiers: SubscriptionTier[] = [
  {
    id: "free",
    name: "Newsletter",
    description: "Stay updated with writing tips and industry insights.",
    price: "Free",
    billingPeriod: "",
    features: [
      "Weekly writing tips",
      "Publishing updates",
      "Author interviews",
      "Free resources",
    ],
    icon: Mail,
  },
  {
    id: "premium",
    name: "Writer's Circle",
    description: "Join our exclusive community with premium benefits.",
    price: "$19",
    billingPeriod: "/month",
    features: [
      "Everything in Newsletter",
      "Private community access",
      "Monthly live Q&A",
      "Priority workshop booking",
      "Member-only resources",
    ],
    icon: Users,
    popular: true,
  },
  {
    id: "mastermind",
    name: "Author Mastermind",
    description: "Elite program for serious authors.",
    price: "$97",
    billingPeriod: "/month",
    features: [
      "Everything in Writer's Circle",
      "Bi-weekly group coaching",
      "Personalized roadmap",
      "Direct access to Sarah",
      "Manuscript critiques",
    ],
    icon: Star,
  },
];

const Subscription = () => {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  const handleSubscribe = async (tierId: string) => {
    setIsSubmitting(true);
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log("Subscribing to:", tierId);
    setIsSubmitting(false);
    alert(`Successfully subscribed!`);
  };

  const handleNewsletterSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;
    await handleSubscribe("free");
    setEmail("");
  };

  return (
    <section id="subscription" ref={ref} className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Star className="w-4 h-4" />
              Join the Community
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Accelerate Your Writing Journey
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              Join thousands of writers who have transformed their craft with
              our comprehensive programs.
            </p>
          </div>

          {/* Subscription Tiers */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {subscriptionTiers.map((tier, index) => {
              const IconComponent = tier.icon;

              return (
                <animated.div
                  key={tier.id}
                  style={{
                    opacity: inView ? 1 : 0,
                    transform: inView ? "translateY(0px)" : "translateY(50px)",
                    transitionDelay: inView ? `${300 + index * 150}ms` : "0ms",
                    transition: "all 0.6s ease-out",
                  }}
                >
                  <Card
                    className={`group h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 interactive-lift relative ${
                      tier.popular
                        ? "ring-2 ring-brand-accent/20 scale-105"
                        : ""
                    }`}
                  >
                    {tier.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <Badge className="bg-brand-accent text-brand-primary">
                          Most Popular
                        </Badge>
                      </div>
                    )}

                    <CardHeader className="text-center">
                      <div className="mx-auto w-16 h-16 bg-brand-accent/10 rounded-2xl flex items-center justify-center mb-4">
                        <IconComponent className="w-8 h-8 text-brand-accent" />
                      </div>

                      <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                        {tier.name}
                      </h3>

                      <p className="text-brand-secondary/70 leading-relaxed mb-4">
                        {tier.description}
                      </p>

                      <div className="text-center">
                        <div className="flex items-end justify-center gap-1">
                          <span className="text-3xl font-bold text-brand-secondary">
                            {tier.price}
                          </span>
                          <span className="text-brand-secondary/60">
                            {tier.billingPeriod}
                          </span>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <div className="space-y-6">
                        {/* Features */}
                        <div className="space-y-3">
                          {tier.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start gap-3">
                              <Check className="w-5 h-5 text-brand-accent flex-shrink-0 mt-0.5" />
                              <span className="text-sm text-brand-secondary/80">
                                {feature}
                              </span>
                            </div>
                          ))}
                        </div>

                        {/* CTA */}
                        <div className="pt-4">
                          {tier.id === "free" ? (
                            <form
                              onSubmit={handleNewsletterSignup}
                              className="space-y-3"
                            >
                              <Input
                                type="email"
                                placeholder="Enter your email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                required
                                className="w-full"
                              />
                              <Button
                                type="submit"
                                variant="outline"
                                className="w-full group"
                                disabled={isSubmitting}
                              >
                                {isSubmitting
                                  ? "Subscribing..."
                                  : "Subscribe Free"}
                                <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                              </Button>
                            </form>
                          ) : (
                            <Button
                              variant={tier.popular ? "hero" : "outline"}
                              className="w-full group"
                              onClick={() => handleSubscribe(tier.id)}
                              disabled={isSubmitting}
                            >
                              {isSubmitting
                                ? "Processing..."
                                : `Choose ${tier.name}`}
                              <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </animated.div>
              );
            })}
          </div>

          {/* Money Back Guarantee */}
          <div className="text-center">
            <div className="bg-brand-neutral/50 rounded-2xl p-8 max-w-2xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-4">
                <div className="w-8 h-8 bg-brand-accent/10 rounded-full flex items-center justify-center">
                  <Check className="w-5 h-5 text-brand-accent" />
                </div>
                <h3 className="text-lg font-serif font-bold text-brand-secondary">
                  30-Day Money-Back Guarantee
                </h3>
              </div>
              <p className="text-brand-secondary/70">
                Not satisfied? Get a full refund within 30 days, no questions
                asked.
              </p>
            </div>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default Subscription;
