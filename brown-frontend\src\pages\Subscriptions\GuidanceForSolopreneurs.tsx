import { useInView } from "react-intersection-observer";
import { useSpring, animated } from "@react-spring/web";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Check,
  MessageCircle,
  Mail,
  Users,
  BookOpen,
  Edit3,
} from "lucide-react";

const GuidanceForSolopreneurs = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [contentRef, contentInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [pricingRef, pricingInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const contentSpring = useSpring({
    opacity: contentInView ? 1 : 0,
    transform: contentInView ? "translateY(0px)" : "translateY(30px)",
  });

  const pricingSpring = useSpring({
    opacity: pricingInView ? 1 : 0,
    transform: pricingInView ? "translateY(0px)" : "translateY(30px)",
  });

  const handleWhatsAppContact = () => {
    const message = encodeURIComponent(
      "Hi! I'm interested in the Guidance for Writing Solopreneurs package. Could you please provide more details?"
    );
    const whatsappUrl = `https://wa.me/message/DOCQNYXAEPVDH1?text=${message}`;
    window.open(whatsappUrl, "_blank", "noopener,noreferrer");
  };

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Hero Section */}
      <animated.section
        ref={heroRef}
        style={heroSpring}
        className="py-20 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-serif text-brown-900 mb-6">
            Guidance for Writing Solopreneurs
          </h1>
          <h2 className="text-xl md:text-2xl text-brown-700 mb-8 leading-relaxed">
            <span className="font-semibold">Christian content writer?</span> Now
            you can have an editor to help you ensure your content is well
            written, piercing, and life-giving.
          </h2>
          <Button
            onClick={handleWhatsAppContact}
            size="lg"
            className="bg-brown-800 hover:bg-brown-900 text-white px-8 py-3"
          >
            <MessageCircle className="w-5 h-5 mr-2" />
            Contact Me
          </Button>
        </div>
      </animated.section>

      {/* Main Content */}
      <animated.section
        ref={contentRef}
        style={contentSpring}
        className="py-16 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-start">
            {/* Left Column - Main Description */}
            <div className="space-y-8">
              <Card className="p-6">
                <CardContent className="space-y-6">
                  <p className="text-lg text-brown-700 leading-relaxed">
                    Are you a follower of Jesus with something to say? Has the
                    Holy Spirit been prompting you? Are you holding back due to
                    uncertainty in your writing skills? Do you need an editor,
                    coach, or encourager? Brown Patience offers help from
                    writing to publishing with the Guidance for Writing
                    Solopreneurs package.
                  </p>

                  <div>
                    <h3 className="text-xl font-serif font-semibold text-brown-900 mb-3">
                      Who is a Writing Solopreneur?
                    </h3>
                    <p className="text-brown-700 leading-relaxed">
                      Writing solopreneurs wield influence with their pens,
                      impacting culture through faith-based writing, including
                      blogs, newsletters, articles, social media, and stories.
                      They have a burning message for the world and know how to
                      scatter their seeds to attract those in need.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-serif font-semibold text-brown-900 mb-3">
                      Who is this Guidance for?
                    </h3>
                    <p className="text-brown-700 leading-relaxed">
                      Guidance for Writing Solopreneurs is for budding and
                      experienced writers needing expert help to refine their
                      work. It offers someone who understands compelling writing
                      to provide opinions, editing, and fine-tuning, ensuring
                      your story is clear, correct, and polished enough to be
                      told.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Button
                onClick={handleWhatsAppContact}
                size="lg"
                className="w-full bg-brown-800 hover:bg-brown-900 text-white"
              >
                <MessageCircle className="w-5 h-5 mr-2" />
                Subscribe
              </Button>
            </div>

            {/* Right Column - What's Included */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl font-serif text-brown-900 flex items-center gap-2">
                    <Edit3 className="w-5 h-5" />
                    What does this Guidance entail?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-3">
                      <Check className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-brown-700">
                        <strong>Editing:</strong> Making it relatable and
                        practical to your readers.
                      </span>
                    </li>
                    <li className="flex items-start gap-3">
                      <Check className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-brown-700">
                        <strong>Posting Guidance:</strong> How do you package
                        it? Social media? A blog? Newsletters? Where's your
                        audience? Do they prefer long or short-form content?
                      </span>
                    </li>
                    <li className="flex items-start gap-3">
                      <Check className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-brown-700">
                        <strong>Critical Feedback On Your Writing:</strong> So
                        you'll know what to do better and where you're already
                        doing great.
                      </span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-xl font-serif text-brown-900 flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    What categories of content does the Guidance cover?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-brown-700 mb-4">
                    With this subscription, you get proofreading, editing, and
                    necessary revisions to:
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-3">
                      <Check className="w-4 h-4 text-green-600" />
                      <span className="text-brown-700">Your blog posts</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-4 h-4 text-green-600" />
                      <span className="text-brown-700">Your articles</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-4 h-4 text-green-600" />
                      <span className="text-brown-700">Your newsletters</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-4 h-4 text-green-600" />
                      <span className="text-brown-700">
                        Your social media content
                      </span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-4 h-4 text-green-600" />
                      <span className="text-brown-700">
                        Your fictional/non-fictional stories
                      </span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </animated.section>

      {/* Pricing Section */}
      <animated.section
        ref={pricingRef}
        style={pricingSpring}
        className="py-16 px-4 sm:px-6 lg:px-8 bg-white"
      >
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-12 text-center">
            Subscription Pricing
          </h2>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <Card className="relative overflow-hidden border-brown-300 shadow-lg">
              <div className="absolute top-0 left-0 right-0 bg-brown-800 text-white text-center py-2 text-sm font-medium">
                Monthly Plan
              </div>
              <CardHeader className="text-center pb-8 pt-12">
                <CardTitle className="text-2xl font-serif text-brown-900">
                  Monthly Subscription
                </CardTitle>
                <div className="text-4xl font-bold text-brown-800 mt-4">
                  ₦15,000
                  <span className="text-lg font-normal text-brown-600">
                    /month
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">
                      Up to 20 write-ups per month
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">
                      Comprehensive editing & feedback
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">
                      Posting guidance & strategy
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">
                      Google Docs & Dropbox collaboration
                    </span>
                  </div>
                </div>
                <Button
                  onClick={handleWhatsAppContact}
                  className="w-full bg-brown-800 hover:bg-brown-900 text-white mt-6"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Subscribe Monthly
                </Button>
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <div className="absolute top-0 left-0 right-0 bg-green-600 text-white text-center py-2 text-sm font-medium">
                Best Value - 20% Off
              </div>
              <CardHeader className="text-center pb-8 pt-12">
                <CardTitle className="text-2xl font-serif text-brown-900">
                  Annual Subscription
                </CardTitle>
                <div className="text-4xl font-bold text-brown-800 mt-4">
                  ₦12,000
                  <span className="text-lg font-normal text-brown-600">
                    /month
                  </span>
                </div>
                <p className="text-sm text-brown-600 mt-2">
                  Paid annually (₦144,000)
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">
                      Everything in Monthly Plan
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">
                      20% discount (Save ₦36,000)
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Priority support</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">
                      5 extra slots per referral
                    </span>
                  </div>
                </div>
                <Button
                  onClick={handleWhatsAppContact}
                  variant="outline"
                  className="w-full border-brown-300 text-brown-700 hover:bg-brown-50 mt-6"
                >
                  Subscribe Annually
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </animated.section>

      {/* FAQ Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-cream-50">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-12 text-center">
            Frequently Asked Questions
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  Am I still in control?
                </h3>
                <p className="text-brown-700">
                  Yes. How much revision your write-up undergoes is up to you. I
                  make the suggestions; you reserve the right to approve or
                  reject them.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  How do we communicate?
                </h3>
                <p className="text-brown-700">
                  We communicate using collaborative technologies such as Google
                  docs and Dropbox. And if WhatsApp will be easier for you, it
                  works perfectly for me.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  Is there a limit to the number of write-ups I can send?
                </h3>
                <p className="text-brown-700">
                  You can send up to 20 write-ups in a month. If you need to
                  have me edit more than 20 write-ups, you will be charged a
                  slight premium.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  Can I get a refund of my monthly subscription?
                </h3>
                <p className="text-brown-700">
                  No you can't. Once a month is gone, the subscription for that
                  month expires, and the unused slots also expire.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  What if I want you to ghostwrite for me?
                </h3>
                <p className="text-brown-700">
                  We'd need to discuss it outside this package. This Guidance
                  package doesn't cover ghostwriting. It only covers the editing
                  of the writing you've already done.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  This package is only for Christian writers?
                </h3>
                <p className="text-brown-700">
                  Yes. This very one is a faith-based package.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  Do I gain extra points for referring someone?
                </h3>
                <p className="text-brown-700">
                  Yes. You get 5 extra write-up slots (in a month) for every
                  person you refer.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  Can you post my write-ups for me after editing them?
                </h3>
                <p className="text-brown-700">
                  No. This Guidance package doesn't include social media, blog,
                  website, or newsletter management. It focuses on ensuring your
                  write-up is clean, sharp, and ready to run.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-8 text-center">
            Ask Your Questions
          </h2>
          <Card>
            <CardContent className="p-6">
              <form className="space-y-6">
                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-brown-900 mb-2"
                  >
                    Your Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-brown-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-brown-900 mb-2"
                  >
                    Your Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    placeholder="Name"
                    className="w-full px-3 py-2 border border-brown-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
                <div>
                  <label
                    htmlFor="message"
                    className="block text-sm font-medium text-brown-900 mb-2"
                  >
                    Your Message
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    placeholder="Leave a comment..."
                    className="w-full px-3 py-2 border border-brown-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  ></textarea>
                </div>
                <Button
                  type="submit"
                  className="w-full bg-brown-800 hover:bg-brown-900 text-white"
                >
                  Send Message
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
};

export default GuidanceForSolopreneurs;
