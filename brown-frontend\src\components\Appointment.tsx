import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, Clock, CheckCircle, ArrowRight } from "lucide-react";

interface AppointmentProps {
  id?: string;
  title?: string;
  subtitle?: string;
  description?: string;
  className?: string;
  backgroundColor?: string;
  onBookingClick?: () => void;
  showFeatures?: boolean;
  features?: string[];
}

const Appointment = ({
  id = "appointment",
  title = "Book a Consultation",
  subtitle = "Schedule Your Session",
  description = "Ready for personalized guidance? Schedule a one-on-one consultation to discuss your writing goals and how I can help you achieve them.",
  className = "",
  backgroundColor = "bg-brand-neutral/30",
  onBookingClick,
  showFeatures = true,
  features = [
    "One-on-one personalized consultation",
    "Detailed project assessment",
    "Customized writing plan",
    "Clear timeline and milestones",
    "Ongoing support and guidance"
  ]
}: AppointmentProps) => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  const handleBookingClick = () => {
    if (onBookingClick) {
      onBookingClick();
    } else {
      // Default booking action - could integrate with Calendly, Acuity, etc.
      console.log("Booking appointment clicked");
      // Example: window.open('https://calendly.com/your-link', '_blank');
    }
  };

  return (
    <section
      id={id}
      ref={ref}
      className={`py-20 ${backgroundColor} ${className}`}
    >
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Calendar className="w-4 h-4" />
              {subtitle}
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              {title}
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              {description}
            </p>
          </div>

          {/* Appointment Card */}
          <div className="max-w-2xl mx-auto">
            <Card className="group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
              <CardContent className="p-8 text-center">
                <div className="w-20 h-20 bg-brand-accent/10 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Calendar className="w-10 h-10 text-brand-accent" />
                </div>
                
                <h3 className="text-2xl font-serif font-bold text-brand-secondary mb-4">
                  Schedule Your Consultation
                </h3>
                
                <p className="text-brand-secondary/70 mb-8 leading-relaxed">
                  Let's discuss your writing goals and create a personalized plan to help you achieve them.
                </p>

                {showFeatures && (
                  <div className="mb-8">
                    <h4 className="text-lg font-serif font-semibold text-brand-secondary mb-4">
                      What You'll Get:
                    </h4>
                    <div className="space-y-3 text-left max-w-md mx-auto">
                      {features.map((feature, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <CheckCircle className="w-5 h-5 text-brand-accent mt-0.5 flex-shrink-0" />
                          <span className="text-brand-secondary/70 text-sm">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                <Button
                  variant="hero"
                  size="lg"
                  onClick={handleBookingClick}
                  className="w-full group/btn"
                >
                  <Calendar className="w-5 h-5 mr-2" />
                  Book Your Appointment
                  <ArrowRight className="w-5 h-5 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                </Button>

                {/* Response Time Notice */}
                <div className="mt-6">
                  <div className="inline-flex items-center gap-2 bg-brand-neutral/50 px-4 py-2 rounded-full text-brand-secondary/70">
                    <Clock className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      Confirmation within 24 hours
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default Appointment;
