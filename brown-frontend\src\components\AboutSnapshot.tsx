import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { ArrowRight, Award } from "lucide-react";
import abtImg from "@/assets/brown/abtImg.webp";

const AboutSnapshot = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const contentSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(30px)",
  });

  const imageSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView
      ? "translateY(0px) rotate(0deg)"
      : "translateY(30px) rotate(-2deg)",
    delay: 200,
  });

  return (
    <section ref={ref} className="py-20 bg-brand-neutral/30">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <animated.div style={contentSpring}>
            <div className="space-y-6">
              <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium">
                <Award className="w-4 h-4" />
                About the Author
              </div>

              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary">
                Hi, I'm <span className="text-brand-accent">Sarah Johnson</span>
              </h2>

              <div className="space-y-4 text-brand-secondary/80 leading-relaxed">
                <p className="text-lg">
                  With over a decade of experience in both writing and
                  publishing, I've helped hundreds of aspiring authors turn
                  their dreams into published reality. My approach combines
                  practical publishing knowledge with the creative courage
                  needed to share your authentic voice with the world.
                </p>

                <p>
                  From traditional publishing houses to the modern indie
                  landscape, I've navigated it all—and I'm here to guide you
                  through your own unique journey. Whether you're writing your
                  first chapter or your fifth book, every writer deserves
                  support, clarity, and encouragement.
                </p>
              </div>

              <div className="flex flex-wrap gap-6 text-brand-secondary/70 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-brand-accent rounded-full"></div>
                  <span>10+ Years Publishing Experience</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-brand-accent rounded-full"></div>
                  <span>200+ Authors Coached</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-brand-accent rounded-full"></div>
                  <span>5 Published Books</span>
                </div>
              </div>

              <Button variant="outline" size="lg" className="group mt-6">
                Read My Full Story
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </animated.div>

          {/* Author Photo */}
          <animated.div style={imageSpring} className="lg:pl-8">
            <div className="relative">
              <div className="aspect-square max-w-md mx-auto rounded-2xl overflow-hidden shadow-elegant">
                <img
                  src={abtImg}
                  alt="Brown Patience- Author and Writing Coach"
                  className="w-full h-full object-cover hover:scale-110 transition-transform duration-500"
                  loading="lazy"
                />
              </div>

              {/* Decorative elements */}
              {/* <div className="absolute -top-4 -left-4 w-24 h-24 bg-brand-accent/10 rounded-full blur-xl"></div>
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-brand-accent/5 rounded-full blur-2xl"></div> */}

              {/* Quote bubble */}
              {/* <div className="absolute -right-4 top-1/4 bg-brand-primary shadow-elegant rounded-2xl p-4 max-w-xs border border-brand-grey/20">
                <p className="text-sm text-brand-secondary italic">
                  "Every writer has a story worth telling. My job is to help you
                  tell it beautifully."
                </p>
              </div> */}
            </div>
          </animated.div>
        </div>
      </div>
    </section>
  );
};

export default AboutSnapshot;
