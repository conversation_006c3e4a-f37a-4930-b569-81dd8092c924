import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import Contact from "@/components/Contact";
import {
  Users,
  Target,
  BookOpen,
  Lightbulb,
  Clock,
  CheckCircle,
  Star,
  ArrowRight,
  MessageCircle,
  TrendingUp,
  Award,
  Heart,
  Zap,
  Calendar,
  PenTool,
  Sparkles,
} from "lucide-react";

const CoachingForWriters = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [servicesRef, servicesInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [benefitsRef, benefitsInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title = "Writing Coaching Services - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const servicesSpring = useSpring({
    opacity: servicesInView ? 1 : 0,
    transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
  });

  const benefitsSpring = useSpring({
    opacity: benefitsInView ? 1 : 0,
    transform: benefitsInView ? "translateY(0px)" : "translateY(50px)",
  });

  // Course objectives and what students learn
  const courseObjectives = [
    "Craft stories that grab the attention of your audience",
    "Sustain that attention throughout your writing",
    "Communicate your message clearly and compellingly",
    "Leave lasting impressions on your readers",
    "Spur readers to action through compelling storytelling",
    "Write like the writers you admire",
  ];

  const courseFeatures = [
    {
      title: "Story-Based Communication",
      description:
        "Learn to put your thoughts together and communicate them clearly, compellingly through stories.",
      icon: BookOpen,
    },
    {
      title: "Correct Writing Techniques",
      description:
        "Master proper writing techniques, observing necessary punctuation marks and grammar.",
      icon: PenTool,
    },
    {
      title: "Audience Targeting",
      description:
        "Learn how to take a message and tailor it to your specific audience effectively.",
      icon: Target,
    },
    {
      title: "Engaging Content Creation",
      description:
        "Make serious topics interesting to read in blog posts, newsletters, articles, and essays.",
      icon: Sparkles,
    },
  ];

  const classSchedule = {
    frequency: "Thrice a week",
    days: ["Mondays", "Wednesdays", "Saturdays"],
    time: "8 PM to 9 PM each day",
    duration: "4 weeks",
    format: "Online via WhatsApp Course Page",
  };

  const testimonials = [
    {
      name: "Sarah-Sesi-Godonu",
      content:
        "I never knew I would need to sharpen my writing skills. It had never crossed my mind to take a writing class because I thought it was for only aspiring writers. That fateful week, Coach Brown reached out to me, because she felt led to send the information to me when the scholarship application was ongoing. I searched my heart, and I remembered that that same week I was to help my sister reply to her client. I noticed her choice of words and searched my heart again. Then I found out that I really needed to sharpen my writing and communication skills.",
      highlight:
        "Inside you is the most authentic story no one else can write. So I have decided not to hold all of these stories back to myself.",
      rating: 5,
    },
    {
      name: "Bukola Adewuyi",
      content:
        "The course began Feb 12, 2024. My expectation was that the class will take me a step further in fulfilling God's plan for my life by making me a great story teller. After the course: 19th April, 2024. I make bold to see that my expectations have been met and surpassed.",
      highlight:
        "I learnt that the basic tactic to present my message is with a STORY. Stories just have a way of keeping people hooked.",
      rating: 5,
    },
    {
      name: "Olamilekan Adenusi",
      content:
        "The Course began February 12, 2024. And I was expecting to learn how to effectively pen down all that my mind saw, pondered, and reflected upon. I had no idea what you would be teaching us when I started this course—except that I should know how to write compelling stories in the end. It's April 15.",
      highlight:
        "And I've learnt so much that I now feel confident when writing. I feel like a writer. I'm beginning to think more like a writer.",
      rating: 5,
    },
    {
      name: "Mercy Adegbenro",
      content:
        "The course began February 12, 2024. My expectation was to be able to express myself better through writing and also that it reflects in my oral communication. After the course: March 9, 2024. I have surpassed my expectations.",
      highlight:
        "Coach did not stop at helping me express myself better through writing but broke things down and made me understand why I am writing the story, who my audience is, the feelings and emotions put into writing.",
      rating: 5,
    },
    {
      name: "Timilehin Oyinloye",
      content:
        "April 30, 2023. At first, when I enrolled into this class, I didn't really have any purpose of joining then. I knew I was lacking in one aspect or the other in terms of punctuations and the rest. Thank God, now I can say that I'm better.",
      highlight:
        "When I joined this class, you were able to open my eyes to the use of commas, letting me know the places where I was using them unnecessarily.",
      rating: 5,
    },
    {
      name: "Surprise Zihlavski",
      content:
        "By the end of the course, I had hoped to gather enough courage to pursue my dream of becoming an author. I am happy to say that I have achieved more than that. Now, I not only have the courage but also the knowledge to begin my journey.",
      highlight:
        "I used to believe I lacked the creativity to write a fictional book, but now, with all I have learned, the only limit is myself.",
      rating: 5,
    },
  ];

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="min-h-screen bg-cream-50 pt-16">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="py-20 bg-gradient-to-br from-brand-primary to-brand-neutral/20"
      >
        <div className="container mx-auto px-4">
          <animated.div
            style={heroSpring}
            className="max-w-4xl mx-auto text-center"
          >
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Users className="w-4 h-4" />
              Writing Coaching & Mentorship
            </div>
            <h1 className="text-4xl md:text-6xl font-serif font-bold text-brand-secondary mb-6">
              Coaching For Writers
            </h1>
            <p className="text-xl text-brand-secondary/70 mb-8 leading-relaxed">
              The sure fire way to get attention with your writing is to tell a
              story. Because stories keep people hooked. And this writing class
              teaches you to tell compelling stories in your writing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="hero"
                size="lg"
                onClick={scrollToContact}
                className="group"
              >
                Start Your Journey
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                Free Discovery Call
              </Button>
            </div>
          </animated.div>
        </div>
      </section>

      {/* Services Section */}
      <section ref={servicesRef} className="py-20">
        <div className="container mx-auto px-4">
          <animated.div style={servicesSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Coaching Programs
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                Choose the coaching approach that best fits your needs, goals,
                and schedule.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {coachingServices.map((service, index) => {
                const IconComponent = service.icon;
                return (
                  <animated.div
                    key={service.title}
                    style={{
                      opacity: servicesInView ? 1 : 0,
                      transform: servicesInView
                        ? "translateY(0px)"
                        : "translateY(50px)",
                      transitionDelay: servicesInView
                        ? `${200 + index * 100}ms`
                        : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                  >
                    <Card
                      className={`h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 relative ${
                        service.popular ? "ring-2 ring-brand-accent/20" : ""
                      }`}
                    >
                      {service.popular && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <span className="bg-brand-accent text-brand-primary px-4 py-1 rounded-full text-xs font-medium">
                            Most Popular
                          </span>
                        </div>
                      )}
                      <CardHeader>
                        <div className="flex items-center gap-4 mb-4">
                          <div className="w-12 h-12 bg-brand-accent/10 rounded-xl flex items-center justify-center">
                            <IconComponent className="w-6 h-6 text-brand-accent" />
                          </div>
                          <div>
                            <h3 className="text-xl font-serif font-bold text-brand-secondary">
                              {service.title}
                            </h3>
                            <p className="text-brand-accent font-medium">
                              {service.price}
                            </p>
                          </div>
                        </div>
                        <p className="text-brand-secondary/70 leading-relaxed">
                          {service.description}
                        </p>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3 mb-6">
                          {service.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start gap-3">
                              <CheckCircle className="w-4 h-4 text-brand-accent mt-0.5 flex-shrink-0" />
                              <span className="text-sm text-brand-secondary/80">
                                {feature}
                              </span>
                            </div>
                          ))}
                        </div>
                        <Button
                          variant={service.popular ? "hero" : "outline"}
                          className="w-full group"
                          onClick={scrollToContact}
                        >
                          Get Started
                          <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                        </Button>
                      </CardContent>
                    </Card>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section ref={benefitsRef} className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <animated.div style={benefitsSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Why Choose Writing Coaching?
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                Personalized support that accelerates your growth and helps you
                achieve your writing goals faster.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {coachingBenefits.map((benefit, index) => {
                const IconComponent = benefit.icon;
                return (
                  <animated.div
                    key={benefit.title}
                    style={{
                      opacity: benefitsInView ? 1 : 0,
                      transform: benefitsInView
                        ? "translateY(0px)"
                        : "translateY(50px)",
                      transitionDelay: benefitsInView
                        ? `${200 + index * 100}ms`
                        : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                    className="text-center"
                  >
                    <div className="w-16 h-16 bg-brand-accent/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="w-8 h-8 text-brand-accent" />
                    </div>
                    <h3 className="text-lg font-serif font-bold text-brand-secondary mb-3">
                      {benefit.title}
                    </h3>
                    <p className="text-brand-secondary/70 text-sm leading-relaxed">
                      {benefit.description}
                    </p>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Success Stories
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              See how coaching has transformed the writing journeys of authors
              just like you.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card
                key={testimonial.name}
                className="hover:shadow-elegant transition-all duration-300"
              >
                <CardContent className="p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-4 h-4 fill-brand-accent text-brand-accent"
                      />
                    ))}
                  </div>
                  <p className="text-brand-secondary/80 mb-4 italic">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <p className="font-semibold text-brand-secondary">
                      {testimonial.name}
                    </p>
                    <p className="text-sm text-brand-secondary/60">
                      {testimonial.role}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Philosophy Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-8">
              My Coaching Philosophy
            </h2>
            <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant">
              <p className="text-lg text-brand-secondary/80 leading-relaxed mb-6 italic">
                "My writing students, my book writing or editing clients, the
                clients that run blogs and newsletters, I'm their chief
                cheerleader!"
              </p>
              <p className="text-lg text-brand-secondary/80 leading-relaxed">
                "You've got a message to share? Please go ahead and share it."
                It's my anthem! I poke them unrelentingly, supporting them to go
                ahead and share their message. Writing is a change-provoking
                art. It's why we read. It's why we write. Let me help you share
                your message clearly and compellingly.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant max-w-4xl mx-auto text-center">
            <h3 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-6">
              Ready to Share Your Message?
            </h3>
            <p className="text-lg text-brand-secondary/70 mb-8 max-w-2xl mx-auto">
              Let's work together to help you share your message clearly and
              compellingly. Every great writer needs a cheerleader, and I'm here
              to support you every step of the way.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="hero"
                size="lg"
                onClick={scrollToContact}
                className="group"
              >
                Schedule Free Call
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CoachingForWriters;
