import { useSpring, animated } from '@react-spring/web';
import { useInView } from 'react-intersection-observer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Calendar, Clock, ArrowRight, PenTool } from 'lucide-react';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  image: string;
  publishDate: string;
  readTime: number;
  slug: string;
}

const blogPosts: BlogPost[] = [
  {
    id: '1',
    title: 'Finding Your Authentic Voice as a Writer',
    excerpt: 'Discover how to break through the noise and develop a writing voice that truly resonates with your readers. Learn practical exercises to uncover your unique perspective.',
    image: '/api/placeholder/400/250',
    publishDate: '2024-01-15',
    readTime: 6,
    slug: 'finding-your-authentic-voice'
  },
  {
    id: '2',
    title: 'The Modern Publishing Landscape: What Authors Need to Know',
    excerpt: 'Navigate the evolving world of publishing with confidence. From traditional routes to indie publishing, understand your options and make informed decisions.',
    image: '/api/placeholder/400/250',
    publishDate: '2024-01-08',
    readTime: 8,
    slug: 'modern-publishing-landscape'
  },
  {
    id: '3',
    title: 'Overcoming Writer\'s Block: 7 Proven Strategies',
    excerpt: 'Transform creative stagnation into momentum with these time-tested techniques. Plus, learn why writer\'s block might actually be a sign of growth.',
    image: '/api/placeholder/400/250',
    publishDate: '2024-01-01',
    readTime: 5,
    slug: 'overcoming-writers-block'
  }
];

const BlogPosts = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? 'translateY(0px)' : 'translateY(50px)',
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <section id="blog" ref={ref} className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <PenTool className="w-4 h-4" />
              Latest Insights
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Writing Wisdom & Publishing Insights
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              Practical advice, industry updates, and creative inspiration for writers at every stage of their journey.
            </p>
          </div>

          {/* Blog Posts Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {blogPosts.map((post, index) => {
              const cardSpring = useSpring({
                opacity: inView ? 1 : 0,
                transform: inView ? 'translateY(0px)' : 'translateY(50px)',
                delay: inView ? 100 + index * 150 : 0,
              });

              return (
                <animated.div key={post.id} style={cardSpring}>
                  <Card className="group h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 interactive-lift">
                    <CardHeader className="p-0">
                      <div className="aspect-[16/10] rounded-t-lg overflow-hidden bg-brand-grey/20">
                        <div className="w-full h-full bg-gradient-to-br from-brand-accent/10 to-brand-accent/20 flex items-center justify-center">
                          <PenTool className="w-12 h-12 text-brand-accent/50" />
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Meta */}
                        <div className="flex items-center gap-4 text-sm text-brand-secondary/60">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {formatDate(post.publishDate)}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {post.readTime} min read
                          </div>
                        </div>

                        {/* Title */}
                        <h3 className="text-xl font-serif font-bold text-brand-secondary group-hover:text-brand-accent transition-colors">
                          {post.title}
                        </h3>

                        {/* Excerpt */}
                        <p className="text-brand-secondary/80 leading-relaxed line-clamp-3">
                          {post.excerpt}
                        </p>

                        {/* Read More Link */}
                        <Button
                          variant="ghost"
                          className="group/btn p-0 h-auto font-medium text-brand-accent hover:bg-transparent"
                        >
                          Read More
                          <ArrowRight className="w-4 h-4 ml-1 group-hover/btn:translate-x-1 transition-transform" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </animated.div>
              );
            })}
          </div>

          {/* View All Posts CTA */}
          <div className="text-center">
            <Button variant="outline" size="lg" className="group">
              View All Posts
              <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default BlogPosts;