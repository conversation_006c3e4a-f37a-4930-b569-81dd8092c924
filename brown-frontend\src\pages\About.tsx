import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import Contact from "@/components/Contact";
import Appointment from "@/components/Appointment";
import { BookOpen, Heart, Target } from "lucide-react";

const About = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [historyRef, historyInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [epiphanyRef, epiphanyInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [companyRef, companyInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const historySpring = useSpring({
    opacity: historyInView ? 1 : 0,
    transform: historyInView ? "translateY(0px)" : "translateY(30px)",
  });

  const epiphanySpring = useSpring({
    opacity: epiphanyInView ? 1 : 0,
    transform: epiphanyInView ? "translateY(0px)" : "translateY(30px)",
  });

  const companySpring = useSpring({
    opacity: companyInView ? 1 : 0,
    transform: companyInView ? "translateY(0px)" : "translateY(30px)",
  });

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Hero Section */}
      <animated.section
        ref={heroRef}
        style={heroSpring}
        className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-brand-primary to-brand-neutral/20"
      >
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-8">
            <img
              src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face"
              alt="Brown Patience - Author and Writing Coach"
              className="w-48 h-48 rounded-full mx-auto object-cover shadow-elegant"
              loading="lazy"
            />
          </div>
          <h1 className="text-4xl md:text-6xl font-serif font-bold text-brand-secondary mb-6">
            About Me
          </h1>
          <blockquote className="text-xl text-brand-secondary/80 max-w-3xl mx-auto leading-relaxed italic mb-8">
            "I'll run the course you lay out for me if you'll just show me how.
            God, teach me lessons for living so I can stay the course. Give me
            insight so I can do what you tell me—my whole life one long,
            obedient response."
          </blockquote>
          <cite className="text-brand-secondary/70 font-medium">
            — Psalms 119:32-34, MSG
          </cite>
        </div>
      </animated.section>

      {/* History Section */}
      <animated.section
        ref={historyRef}
        style={historySpring}
        className="py-20 px-4 sm:px-6 lg:px-8 bg-white"
      >
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <BookOpen className="w-4 h-4" />
              My Journey
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Where It Began For Brown Patience
            </h2>
          </div>
          <div className="prose prose-lg max-w-none text-brand-secondary/80 leading-relaxed">
            <p className="mb-6 text-lg">
              I remember having this 40 or 60-leaves book back in secondary
              school. That was 2014. I was in SS2 (if you're unfamiliar with
              Nigeria's education system, SS2 is one class away from highschool
              graduation). I wrote stories in that notebook. A few friends read
              them and I was so proud of those stories. Then the book got
              missing. It was painful. So painful that I haven't forgotten the
              feeling.
            </p>
            <p className="mb-6 text-lg">
              Still in that 2014, I won a national essay writing competition.
              The way the newspapers put it, they were shocked a public school
              pupil had come out tops. I still giggle when I read that headline.
            </p>
            <p className="mb-6 text-lg">
              As a teen, I wrote for fun. I wrote to relieve boredom. I could be
              sitting in a place and mentally remove myself from there by
              scribbling a letter to an imaginary cousin in Vienna. Nevermind
              that I had no idea if Vienna was actually a place. Writing was how
              I went to places I wished I could be. As soon as the English
              Language teacher said, "Write a letter to your uncle in London,"
              it was my lucky day!
            </p>
            <p className="mb-6 text-lg">
              All these should have told me writing was the path for me, but I
              couldn't have known. I said I'd be a banker when I grew up; I set
              my mind on pursuing accounting in college. And that's precisely
              what I did. Yet purpose can and will find you.
            </p>
          </div>
        </div>
      </animated.section>

      {/* Epiphany Section */}
      <animated.section
        ref={epiphanyRef}
        style={epiphanySpring}
        className="py-20 px-4 sm:px-6 lg:px-8 bg-brand-neutral/30"
      >
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Heart className="w-4 h-4" />
              The Turning Point
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              The Epiphany
            </h2>
          </div>
          <div className="prose prose-lg max-w-none text-brand-secondary/80 leading-relaxed">
            <p className="mb-6 text-lg">
              I wasn't bad at accounting as an undergrad. In fact, I gained a
              reputation in my set as one of the people with the most
              explanatory course notes. You want a person that can write down
              everything the lecturer dictated or explained? Brown Patience is
              one of them! Still, it didn't click. Writing didn't take
              preeminence in my pursuits. Not yet.
            </p>
            <p className="mb-6 text-lg">
              I was in my finals at the University of Lagos when the pandemic
              sent us home for 9 months. 2020, yeah. In those 9 months, I had
              nothing to do but read. And write. And pray. I worried too.
              Worried about what I was supposed to do to make ends meet. When we
              resumed, accounting was no longer looking like the road for me.
            </p>
            <p className="mb-6 text-lg">
              During those 9 months when I did a lot of reading and writing,
              worrying and praying, I got an answer. I like to tell my friends
              that the pandemic slowed us down long enough to find ourselves. We
              slowed down long enough to hear our own thoughts, to find what
              we'd rather pursue.
            </p>
            <p className="mb-6 text-lg">
              It had always been there — on the inside of us — but we had always
              been busy with the tasks at hand, excelling at whatever we found
              to do, almost never giving passion a thought. I have friends who
              returned from the pandemic and made U-turns. I was one of those
              who slowed down long enough to realize I wanted to write.
            </p>
            <div className="bg-brand-primary rounded-2xl p-8 mt-8">
              <p className="text-lg text-brand-secondary/80 leading-relaxed italic text-center">
                "God finally got through to me. I began giving writing a good
                look. It came when I asked, 'God, what should I be doing?' So if
                you ask me what my motivation is, what my reason is, I'll say
                'God.' A thousand times over."
              </p>
            </div>
          </div>
        </div>
      </animated.section>

      {/* The Brown Patience Company Section */}
      <animated.section
        ref={companyRef}
        style={companySpring}
        className="py-20 px-4 sm:px-6 lg:px-8 bg-white"
      >
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Target className="w-4 h-4" />
              Our Mission
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              The Story Of Brown Patience
            </h2>
          </div>
          <div className="prose prose-lg max-w-none text-brand-secondary/80 leading-relaxed">
            <p className="mb-6 text-lg">
              I consider writing a change-provoking art. Books have molded my
              life and the lives of many before now. Every person who has ever
              had anything to say has encouraged us to glean the wisdom in
              books. So I encourage others to write. I help them write. I poke
              them unrelentingly, supporting them to go ahead and share their
              message.
            </p>
            <p className="mb-6 text-lg">
              My writing students, my book writing or editing clients, the
              clients that run blogs and newsletters, I'm their chief
              cheerleader! "You've got a message to share? Please go ahead and
              share it." It's my anthem!
            </p>
            <p className="mb-6 text-lg">
              Writing is a change-provoking art. It's why we read. It's why we
              write. It's why The Brown Patience Company exists. To ensure you
              have all the help you need to share the message you need to share
              — clearly, compellingly. And then the harvest! We wait for the
              harvest in the lives of people. For surely, the harvest comes.
            </p>

            {/* Stats */}
            <div className="grid md:grid-cols-4 gap-8 mt-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-accent mb-2">
                  5+
                </div>
                <div className="text-brand-secondary/70 text-sm">
                  Years of Experience
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-accent mb-2">
                  50+
                </div>
                <div className="text-brand-secondary/70 text-sm">
                  Books Published
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-accent mb-2">
                  200+
                </div>
                <div className="text-brand-secondary/70 text-sm">
                  Writers Coached
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-accent mb-2">
                  15+
                </div>
                <div className="text-brand-secondary/70 text-sm">
                  Best Selling Books
                </div>
              </div>
            </div>
          </div>
        </div>
      </animated.section>

      {/* Faith Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-brand-neutral/30">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-8">
            My Foundation
          </h2>
          <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant">
            <p className="text-lg text-brand-secondary/80 leading-relaxed mb-6">
              I'm a Christian! You may have figured that out by now. I follow
              Jesus. I want to keep following Jesus. Outside God, without God's
              leading, there's nothing to Brown Patience. Had God not led me
              here, I wouldn't be here.
            </p>
            <p className="text-lg text-brand-secondary/80 leading-relaxed mb-6">
              In the last quarter of 2021, the Lord had simply said to me, "I
              want you to teach story writing." Look where we are now. I have no
              other plan for my life but to follow God's plan.
            </p>
            <blockquote className="text-lg text-brand-secondary/80 leading-relaxed italic">
              "Over my life, He'll reap much harvest. And I'll be able to say,
              like Jesus, 'I have brought you glory on earth by finishing the
              work you gave me to do' (John 17:4, NIV)."
            </blockquote>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <Contact
        title="Ready to Work Together?"
        description="Let's bring your writing dreams to life. Whether you need coaching, editing, or guidance on your publishing journey, I'm here to help."
        whatsappMessage="Hi Patience! I'd love to learn more about your writing services and how you can help me with my project."
      />

      {/* Appointment Section */}
      <Appointment
        title="Schedule a Discovery Call"
        description="Let's discuss your writing goals and how I can help you achieve them through personalized coaching and support."
        features={[
          "Understand your writing goals",
          "Assess your current skill level",
          "Create a personalized development plan",
          "Discuss available programs and services",
          "Answer all your questions",
        ]}
      />
    </div>
  );
};

export default About;
