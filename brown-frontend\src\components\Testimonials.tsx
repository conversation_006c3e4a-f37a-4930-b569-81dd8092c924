import { useState } from 'react';
import { useSpring, animated } from '@react-spring/web';
import { useInView } from 'react-intersection-observer';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON>ronLef<PERSON>, <PERSON><PERSON>ron<PERSON><PERSON>, <PERSON>uo<PERSON>, <PERSON> } from 'lucide-react';

interface Testimonial {
  id: string;
  quote: string;
  author: string;
  role: string;
  rating: number;
}

const testimonials: Testimonial[] = [
  {
    id: '1',
    quote: "<PERSON>'s guidance transformed not just my writing, but my entire approach to storytelling. Her insights into character development and narrative structure helped me land my first publishing deal.",
    author: "<PERSON>",
    role: "Debut Novelist",
    rating: 5
  },
  {
    id: '2', 
    quote: "Working with <PERSON> was the turning point in my writing career. Her publishing expertise and editorial eye helped me navigate the industry with confidence and clarity.",
    author: "<PERSON>",
    role: "Indie Author",
    rating: 5
  },
  {
    id: '3',
    quote: "The practical advice and emotional support Sarah provides is unmatched. She doesn't just teach writing—she helps you find your voice and the courage to share it with the world.",
    author: "<PERSON>",
    role: "Memoir Writer",
    rating: 5
  },
  {
    id: '4',
    quote: "<PERSON>'s coaching gave me the tools and confidence I needed to finish my first novel. Her approach is both encouraging and professionally rigorous.",
    author: "<PERSON>",
    role: "First-time Author",
    rating: 5
  }
];

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? 'translateY(0px)' : 'translateY(50px)',
  });

  const slideSpring = useSpring({
    transform: `translateX(-${currentIndex * 100}%)`,
    config: { tension: 200, friction: 25 }
  });

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  return (
    <section ref={ref} className="py-20 bg-brand-neutral/30">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Quote className="w-4 h-4" />
              Client Success Stories
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              What Writers Are Saying
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              Real stories from writers who've transformed their craft and achieved their publishing goals.
            </p>
          </div>

          {/* Testimonials Carousel */}
          <div className="max-w-4xl mx-auto">
            <div className="relative overflow-hidden rounded-2xl">
              <animated.div
                style={slideSpring}
                className="flex"
              >
                {testimonials.map((testimonial) => (
                  <div key={testimonial.id} className="w-full flex-shrink-0">
                    <Card className="mx-4 shadow-elegant border-0 bg-brand-primary/95">
                      <CardContent className="p-8 md:p-12 text-center">
                        {/* Quote Icon */}
                        <div className="w-16 h-16 bg-brand-accent/10 rounded-full flex items-center justify-center mx-auto mb-6">
                          <Quote className="w-8 h-8 text-brand-accent" />
                        </div>

                        {/* Stars */}
                        <div className="flex justify-center gap-1 mb-6">
                          {[...Array(testimonial.rating)].map((_, i) => (
                            <Star key={i} className="w-5 h-5 fill-brand-accent text-brand-accent" />
                          ))}
                        </div>

                        {/* Quote */}
                        <blockquote className="text-lg md:text-xl text-brand-secondary/90 leading-relaxed mb-8 italic font-serif">
                          "{testimonial.quote}"
                        </blockquote>

                        {/* Author */}
                        <div>
                          <div className="font-semibold text-brand-secondary text-lg">
                            {testimonial.author}
                          </div>
                          <div className="text-brand-secondary/60 text-sm">
                            {testimonial.role}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </animated.div>
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-center gap-4 mt-8">
              <Button
                variant="ghost"
                size="icon"
                onClick={prevSlide}
                className="rounded-full hover:bg-brand-accent/10"
              >
                <ChevronLeft className="w-5 h-5" />
              </Button>

              {/* Dots */}
              <div className="flex gap-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentIndex
                        ? 'bg-brand-accent scale-110'
                        : 'bg-brand-grey hover:bg-brand-accent/50'
                    }`}
                  />
                ))}
              </div>

              <Button
                variant="ghost"
                size="icon"
                onClick={nextSlide}
                className="rounded-full hover:bg-brand-accent/10"
              >
                <ChevronRight className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default Testimonials;