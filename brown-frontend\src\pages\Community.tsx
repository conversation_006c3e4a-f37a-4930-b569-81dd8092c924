import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Users,
  MessageCircle,
  BookOpen,
  Calendar,
  Heart,
  Star,
  ArrowRight,
  Coffee,
  Lightbulb,
  Award,
  Globe,
  Zap,
  Target,
} from "lucide-react";

const Community = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [featuresRef, featuresInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [eventsRef, eventsInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title = "Writer's Community - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const featuresSpring = useSpring({
    opacity: featuresInView ? 1 : 0,
    transform: featuresInView ? "translateY(0px)" : "translateY(50px)",
  });

  const eventsSpring = useSpring({
    opacity: eventsInView ? 1 : 0,
    transform: eventsInView ? "translateY(0px)" : "translateY(50px)",
  });

  const communityFeatures = [
    {
      title: "Monthly Writing Challenges",
      description: "Structured prompts and goals to keep your creativity flowing and build consistent writing habits.",
      icon: Target,
      benefits: [
        "Weekly themed prompts",
        "Progress tracking",
        "Peer accountability",
        "Monthly prizes and recognition",
      ],
    },
    {
      title: "Virtual Writing Retreats",
      description: "Quarterly online retreats with focused writing time, workshops, and community connection.",
      icon: Coffee,
      benefits: [
        "4-hour focused writing sessions",
        "Guest author presentations",
        "Breakout room discussions",
        "Resource sharing",
      ],
    },
    {
      title: "Critique Partners Program",
      description: "Get matched with fellow writers for ongoing manuscript feedback and support.",
      icon: Users,
      benefits: [
        "Skill-level matching",
        "Genre-specific partnerships",
        "Structured feedback guidelines",
        "Long-term writing relationships",
      ],
    },
    {
      title: "Expert Guest Sessions",
      description: "Monthly sessions with published authors, agents, and industry professionals.",
      icon: Star,
      benefits: [
        "Industry insights",
        "Q&A opportunities",
        "Networking possibilities",
        "Recorded sessions for later viewing",
      ],
    },
    {
      title: "Resource Library",
      description: "Exclusive access to writing guides, templates, and tools to support your journey.",
      icon: BookOpen,
      benefits: [
        "Writing craft guides",
        "Publishing roadmaps",
        "Template library",
        "Tool recommendations",
      ],
    },
    {
      title: "Success Celebrations",
      description: "A supportive space to share wins, milestones, and celebrate each other's achievements.",
      icon: Award,
      benefits: [
        "Weekly win sharing",
        "Milestone recognition",
        "Publication announcements",
        "Community support",
      ],
    },
  ];

  const upcomingEvents = [
    {
      title: "New Year Writing Goals Workshop",
      date: "January 15, 2024",
      time: "2:00 PM EST",
      description: "Set achievable writing goals and create a roadmap for success in the new year.",
      type: "Workshop",
    },
    {
      title: "Character Development Masterclass",
      date: "January 22, 2024",
      time: "7:00 PM EST",
      description: "Deep dive into creating compelling, three-dimensional characters with guest author Sarah Chen.",
      type: "Masterclass",
    },
    {
      title: "Virtual Writing Sprint",
      date: "January 29, 2024",
      time: "10:00 AM EST",
      description: "3-hour focused writing session with community support and accountability.",
      type: "Writing Sprint",
    },
  ];

  const testimonials = [
    {
      name: "Maria Rodriguez",
      role: "Fiction Writer",
      content: "This community gave me the confidence to finish my first novel. The support and feedback were invaluable.",
      avatar: "M",
    },
    {
      name: "James Thompson",
      role: "Memoir Writer",
      content: "I found my critique partner here, and we've been supporting each other's writing for over a year now.",
      avatar: "J",
    },
    {
      name: "Lisa Park",
      role: "Poetry & Essays",
      content: "The monthly challenges pushed me out of my comfort zone and helped me discover new aspects of my voice.",
      avatar: "L",
    },
  ];

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="min-h-screen bg-cream-50 pt-16">
      {/* Hero Section */}
      <section ref={heroRef} className="py-20 bg-gradient-to-br from-brand-primary to-brand-neutral/20">
        <div className="container mx-auto px-4">
          <animated.div style={heroSpring} className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Users className="w-4 h-4" />
              Writer's Community
            </div>
            <h1 className="text-4xl md:text-6xl font-serif font-bold text-brand-secondary mb-6">
              Where Writers Thrive Together
            </h1>
            <p className="text-xl text-brand-secondary/70 mb-8 leading-relaxed">
              Join a supportive community of writers who understand your journey. Get feedback, find accountability partners, 
              and celebrate your wins in a space designed for growth and connection.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="hero" size="lg" onClick={scrollToContact} className="group">
                Join Our Community
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                Learn More
              </Button>
            </div>
            
            {/* Community Stats */}
            <div className="grid grid-cols-3 gap-8 mt-12 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-accent mb-2">500+</div>
                <div className="text-sm text-brand-secondary/70">Active Writers</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-accent mb-2">50+</div>
                <div className="text-sm text-brand-secondary/70">Published Authors</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-accent mb-2">100+</div>
                <div className="text-sm text-brand-secondary/70">Success Stories</div>
              </div>
            </div>
          </animated.div>
        </div>
      </section>

      {/* Community Features */}
      <section ref={featuresRef} className="py-20">
        <div className="container mx-auto px-4">
          <animated.div style={featuresSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                What Makes Our Community Special
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                More than just a writing group - we're a comprehensive support system for your writing journey.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {communityFeatures.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <animated.div
                    key={feature.title}
                    style={{
                      opacity: featuresInView ? 1 : 0,
                      transform: featuresInView ? "translateY(0px)" : "translateY(50px)",
                      transitionDelay: featuresInView ? `${200 + index * 100}ms` : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                  >
                    <Card className="h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                      <CardHeader>
                        <div className="w-12 h-12 bg-brand-accent/10 rounded-xl flex items-center justify-center mb-4">
                          <IconComponent className="w-6 h-6 text-brand-accent" />
                        </div>
                        <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                          {feature.title}
                        </h3>
                        <p className="text-brand-secondary/70 leading-relaxed">
                          {feature.description}
                        </p>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {feature.benefits.map((benefit, idx) => (
                            <div key={idx} className="flex items-start gap-2">
                              <Heart className="w-3 h-3 text-brand-accent mt-1 flex-shrink-0" />
                              <span className="text-sm text-brand-secondary/80">{benefit}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Upcoming Events */}
      <section ref={eventsRef} className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <animated.div style={eventsSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Upcoming Community Events
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                Regular events designed to inspire, educate, and connect our writing community.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {upcomingEvents.map((event, index) => (
                <animated.div
                  key={event.title}
                  style={{
                    opacity: eventsInView ? 1 : 0,
                    transform: eventsInView ? "translateY(0px)" : "translateY(50px)",
                    transitionDelay: eventsInView ? `${200 + index * 100}ms` : "0ms",
                    transition: "all 0.6s ease-out",
                  }}
                >
                  <Card className="hover:shadow-elegant transition-all duration-300">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-4">
                        <span className="bg-brand-accent/10 text-brand-accent px-3 py-1 rounded-full text-xs font-medium">
                          {event.type}
                        </span>
                        <Calendar className="w-4 h-4 text-brand-secondary/60" />
                      </div>
                      <h3 className="text-lg font-serif font-bold text-brand-secondary mb-2">
                        {event.title}
                      </h3>
                      <div className="text-sm text-brand-secondary/60 mb-3">
                        {event.date} • {event.time}
                      </div>
                      <p className="text-brand-secondary/70 text-sm leading-relaxed">
                        {event.description}
                      </p>
                    </CardHeader>
                    <CardContent>
                      <Button variant="outline" className="w-full group">
                        Register Now
                        <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </CardContent>
                  </Card>
                </animated.div>
              ))}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Community Success Stories
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              Hear from writers who have found their voice and achieved their goals within our community.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={testimonial.name} className="hover:shadow-elegant transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center text-brand-primary font-bold">
                      {testimonial.avatar}
                    </div>
                    <div>
                      <p className="font-semibold text-brand-secondary">{testimonial.name}</p>
                      <p className="text-sm text-brand-secondary/60">{testimonial.role}</p>
                    </div>
                  </div>
                  <p className="text-brand-secondary/80 italic">
                    "{testimonial.content}"
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant max-w-4xl mx-auto text-center">
            <h3 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-6">
              Ready to Join Our Writing Family?
            </h3>
            <p className="text-lg text-brand-secondary/70 mb-8 max-w-2xl mx-auto">
              Take the next step in your writing journey. Join a community that believes in your potential 
              and is committed to helping you achieve your writing dreams.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="hero" size="lg" onClick={scrollToContact} className="group">
                Join Today - $29/month
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                Free Trial Week
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Community;
