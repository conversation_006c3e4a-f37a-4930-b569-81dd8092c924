import { useState, useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { useSpring, animated } from "@react-spring/web";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Mock data - replace with Firestore data
const mockBooks = [
  {
    id: "1",
    slug: "finding-your-voice",
    title: "Finding Your Voice: A Writer's Journey",
    shortBlurb:
      "Discover the unique voice that makes your writing unforgettable.",
    coverUrl:
      "https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400&h=600&fit=crop",
    publicationDate: "2023-06-15",
    tags: ["Writing Craft", "Self-Help"],
    buyLinks: [
      { platform: "Amazon", url: "https://amazon.com/book1" },
      { platform: "Barnes & Noble", url: "https://bn.com/book1" },
    ],
  },
  {
    id: "2",
    slug: "storytelling-mastery",
    title: "Storytelling Mastery",
    shortBlurb: "Master the art of compelling storytelling across all genres.",
    coverUrl:
      "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=600&fit=crop",
    publicationDate: "2023-03-20",
    tags: ["Storytelling", "Fiction"],
    buyLinks: [{ platform: "Amazon", url: "https://amazon.com/book2" }],
  },
];

const BooksList = () => {
  const [books, setBooks] = useState(mockBooks);
  const [filteredBooks, setFilteredBooks] = useState(mockBooks);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTag, setSelectedTag] = useState("all");

  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [booksRef, booksInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const booksSpring = useSpring({
    opacity: booksInView ? 1 : 0,
    transform: booksInView ? "translateY(0px)" : "translateY(30px)",
  });

  // Get all unique tags
  const allTags = [...new Set(books.flatMap((book) => book.tags))];

  // Filter books based on search and tag
  useEffect(() => {
    let filtered = books;

    if (searchTerm) {
      filtered = filtered.filter(
        (book) =>
          book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          book.shortBlurb.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedTag !== "all") {
      filtered = filtered.filter((book) => book.tags.includes(selectedTag));
    }

    setFilteredBooks(filtered);
  }, [searchTerm, selectedTag, books]);

  const handleBuyClick = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Hero Section */}
      <animated.section
        ref={heroRef}
        style={heroSpring}
        className="py-20 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-serif text-brown-900 mb-6">
            My Books
          </h1>
          <p className="text-xl text-brown-700 max-w-2xl mx-auto leading-relaxed">
            A collection of books designed to help writers find their voice,
            master their craft, and share their stories with the world.
          </p>
        </div>
      </animated.section>

      {/* Filters */}
      <section className="py-8 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <div className="flex-1">
              <Input
                placeholder="Search books..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="md:w-48">
              <Select value={selectedTag} onValueChange={setSelectedTag}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by tag" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tags</SelectItem>
                  {allTags.map((tag) => (
                    <SelectItem key={tag} value={tag}>
                      {tag}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </section>

      {/* Books Grid */}
      <animated.section
        ref={booksRef}
        style={booksSpring}
        className="py-16 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredBooks.map((book) => (
              <Card
                key={book.id}
                className="overflow-hidden hover:shadow-lg transition-shadow group"
              >
                <div className="aspect-[3/4] overflow-hidden">
                  <img
                    src={book.coverUrl}
                    alt={`${book.title} cover`}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    loading="lazy"
                  />
                </div>
                <CardContent className="p-6">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {book.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <h3 className="text-xl font-serif text-brown-900 mb-2 group-hover:text-brown-700 transition-colors">
                    {book.title}
                  </h3>
                  <p className="text-brown-600 mb-4 line-clamp-3">
                    {book.shortBlurb}
                  </p>
                  <div className="text-sm text-brown-500 mb-4">
                    Published:{" "}
                    {new Date(book.publicationDate).toLocaleDateString()}
                  </div>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button
                      onClick={() => handleBuyClick(book.buyLinks[0].url)}
                      className="bg-brown-800 hover:bg-brown-900 text-white flex-1"
                    >
                      Buy Now
                    </Button>
                    <Button
                      asChild
                      variant="outline"
                      className="border-brown-300 text-brown-700 hover:bg-brown-50 flex-1"
                    >
                      <Link to={`/books/${book.slug}`}>Read More</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredBooks.length === 0 && (
            <div className="text-center py-12">
              <p className="text-brown-600 text-lg">
                No books found matching your criteria.
              </p>
            </div>
          )}
        </div>
      </animated.section>
    </div>
  );
};

export default BooksList;
