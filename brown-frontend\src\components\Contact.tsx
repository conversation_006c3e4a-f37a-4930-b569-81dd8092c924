import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MessageCircle, Calendar, Clock, Phone } from "lucide-react";

interface ContactProps {
  id?: string;
  title?: string;
  subtitle?: string;
  description?: string;
  whatsappMessage?: string;
  whatsappNumber?: string;
  showPhone?: boolean;
  showEmail?: boolean;
  phoneNumber?: string;
  email?: string;
  className?: string;
  backgroundColor?: string;
}

const Contact = ({
  id = "contact",
  title = "Ready to Start Your Writing Journey?",
  subtitle = "Let's Connect",
  description = "Whether you're just beginning or looking to take your writing to the next level, I'm here to help guide your journey.",
  whatsappMessage = "Hi! I'm interested in learning more about your writing services. Could we schedule a time to chat?",
  whatsappNumber = "1234567890",
  showPhone = true,
  showEmail = true,
  phoneNumber = "+****************",
  email = "<EMAIL>",
  className = "",
  backgroundColor = "bg-background",
}: ContactProps) => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  const handleWhatsAppClick = () => {
    const message = encodeURIComponent(whatsappMessage);
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    window.open(whatsappUrl, "_blank", "noopener,noreferrer");
  };

  const handleBookingClick = () => {
    // In a real implementation, this would link to a booking system
    console.log("Booking appointment clicked");
  };

  return (
    <section
      id={id}
      ref={ref}
      className={`py-20 ${backgroundColor} ${className}`}
    >
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <MessageCircle className="w-4 h-4" />
              {subtitle}
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              {title}
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              {description}
            </p>
          </div>

          {/* Contact Options */}
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-12">
            {/* WhatsApp Contact */}
            <Card className="group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <MessageCircle className="w-8 h-8 text-green-600" />
                </div>

                <h3 className="text-xl font-serif font-bold text-brand-secondary mb-3">
                  Quick Message
                </h3>

                <p className="text-brand-secondary/70 mb-6 leading-relaxed">
                  Have a quick question or want to learn more about my services?
                  Send me a WhatsApp message for a fast, friendly response.
                </p>

                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleWhatsAppClick}
                  className="w-full group/btn border-green-500 text-green-600 hover:bg-green-500 hover:text-white"
                >
                  <MessageCircle className="w-5 h-5 mr-2" />
                  Message on WhatsApp
                </Button>
              </CardContent>
            </Card>

            {/* Appointment Booking */}
            <Card className="group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-brand-accent/10 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Calendar className="w-8 h-8 text-brand-accent" />
                </div>

                <h3 className="text-xl font-serif font-bold text-brand-secondary mb-3">
                  Book a Consultation
                </h3>

                <p className="text-brand-secondary/70 mb-6 leading-relaxed">
                  Ready for personalized guidance? Schedule a one-on-one
                  consultation to discuss your writing goals and how I can help
                  you achieve them.
                </p>

                <Button
                  variant="hero"
                  size="lg"
                  onClick={handleBookingClick}
                  className="w-full"
                >
                  <Calendar className="w-5 h-5 mr-2" />
                  Book an Appointment
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Response Time Notice */}
          <div className="text-center">
            <div className="inline-flex items-center gap-2 bg-brand-neutral/50 px-6 py-3 rounded-full text-brand-secondary/70">
              <Clock className="w-4 h-4" />
              <span className="text-sm font-medium">
                Responses typically within 48 hours
              </span>
            </div>
          </div>

          {/* Additional Contact Info */}
          {(showPhone || showEmail) && (
            <div className="mt-16 text-center">
              <h3 className="text-lg font-serif font-semibold text-brand-secondary mb-4">
                Prefer a Different Way to Connect?
              </h3>
              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center text-brand-secondary/70">
                {showPhone && (
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    <span>{phoneNumber}</span>
                  </div>
                )}
                {showPhone && showEmail && (
                  <div className="hidden sm:block w-1 h-1 bg-brand-secondary/30 rounded-full"></div>
                )}
                {showEmail && (
                  <div className="flex items-center gap-2">
                    <MessageCircle className="w-4 h-4" />
                    <span>{email}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </animated.div>
      </div>
    </section>
  );
};

export default Contact;
