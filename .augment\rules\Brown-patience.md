---
type: "always_apply"
---

# Full build guideline — The Brown Patience Company

# 1) Project summary (one-sentence)

Build a lightweight, high-performance author portfolio site that showcases books (links to external stores), captures email leads into an admin dashboard, hosts a blog with moderated guest comments, and provides a small admin dashboard (subdomain) for full CRUD on books, posts, testimonials, media and messages. Tech: React + Vite, Tailwind, Radix UI, Animate.css + vanilla CSS, react-spring, <PERSON><PERSON> (sparingly), Firebase (Firestore + Auth + Functions), Cloudinary for images, Vercel for admin hosting, WhoGhost cPanel for public site.

Overall UX & Visual Summary

Tone: warm, supportive, confident. Serif headline + readable sans for body.

Palette: use provided brand colors; until provided, use warm browns + cream neutrals for mockups.

Layout: mobile-first, responsive breakpoints at 640, 768, 1024, 1280px.

Performance target: FCP < 1s on mid-tier mobile; critical JS minimal; images lazy-loaded and served via Cloudinary transforms.

Accessibility: WCAG AA baseline, lang="en", semantic HTML, aria attributes, keyboard friendly, reduced-motion toggle visible in footer.

---

# 2) High-level architecture

- **Public frontend** (`brownpatience-frontend`) — static site served from WhoGhost cPanel; React + Vite + Tailwind; reads data from Firestore via public reads or via serverless `/api` endpoints (for private flows like recaptcha-verified subscribe).
- **Admin frontend** (`brownpatience-admin`) — React + Vite + Tailwind; hosted on Vercel; protected by Firebase Auth; uses Firestore and Cloud Functions for server ops.
- **Server functions** (`brownpatience-functions`) — Firebase Cloud Functions (Node/TS): verify reCAPTCHA, write subscribers, moderate comments, send emails (nodemailer/SendGrid), possible rate-limiting or proxy to Cloudinary.
- **Media** — Cloudinary for uploads/transformations; admin uploads signed via server function.
- **Analytics & tools** — GA4 (consent-based), reCAPTCHA, JotForm chatbot embed.
- **DNS/hosting** — main domain on WhoGhost cPanel; `admin.` subdomain pointing to Vercel.
  Tech stack (must use):

Frontend (public site): React + Vite, Tailwind CSS, Radix UI for components, Animate.css + vanilla CSS for simple animations, react-spring for physics-based micro-interactions, Lottie for optional animated icons (lazy-loaded).

Admin dashboard: React + Vite, Tailwind CSS, Radix UI, react-spring.

Backend: Firebase (Firestore, Auth, Cloud Functions), Cloudinary for media storage/transformations, Vercel for admin hosting (free tier), WhoGhost cPanel hosting for public site static deployment.

Analytics & tools: Google Analytics (GA4), ConvertKit for newsletter, JotForm embed for AI chatbot, reCAPTCHA for guest submissions.

Comments: guest comments with moderation, CAPTCHA and language filter.

Non-functional requirements

Very lightweight: prioritize small bundle sizes and route-based code splitting. Aim for critical bundle <150 KB gzipped where possible.

Fast: FCP targeted <1s on mobile (optimizations: preloads, responsive images via Cloudinary transforms, lazy Lottie).

Accessible: WCAG AA baseline, keyboard navigation, alt texts, color contrast check, reduced-motion toggle.

Mobile-first responsive, polished micro-interactions, and subtle, professional/cozy visual language (use provided palette & fonts when available).

Toggleable/reduced motion for accessibility; ability to globally disable animations via user-setting or prefers-reduced-motion.

All user-facing content in English; international audience.

Repo layout (two codebases to keep things separated)

brownpatience-frontend/ — public site (deploy static build to WhoGhost cPanel).

brownpatience-admin/ — admin dashboard (deploy to Vercel — subdomain e.g., admin.thebrownpatiencecompany.com.ng).

brownpatience-functions/ — Firebase Cloud Functions (emails, webhooks, server-side logic).

infra/ — deployment scripts, sample .env.example, Cloudinary/ConvertKit/reCAPTCHA setup docs.

Environment variables (examples)

VITE_FIREBASE_API_KEY, VITE_FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL (for server functions, not front-end),

CLOUDINARY_CLOUD_NAME, CLOUDINARY_UPLOAD_PRESET, CLOUDINARY_API_KEY, CLOUDINARY_API_SECRET (secret kept server-side),

CONVERTKIT_API_KEY, CONVERTKIT_FORM_ID,

GA_MEASUREMENT_ID,

RECAPTCHA_SITE_KEY (frontend), RECAPTCHA_SECRET_KEY (server),

ADMIN_NOTIFICATION_EMAIL (the single admin Gmail address you’ll provide),

VERCEL_URL, CPANEL_FTP_HOST, etc.

---

# 3) Priorities & MVP scope

MVP MUST HAVE (launch list):

1. Homepage (hero, featured books with external buy links, newsletter form that writes to `subscribers` in Firestore via server-side recaptcha verification).
2. Books showcase (index + detail pages) — read-only cards linking to external stores.
3. Blog (create in admin & publish to public) + guest comments with moderation (comments are `pending` until approved).
4. Admin dashboard (books CRUD, posts CRUD, testimonials, messages inbox, subscribers view, comments moderation).
5. Booking/contact flows: WhatsApp contact link + manual booking form stored to `messages` with email notification to single admin email.
6. Media manager using Cloudinary.

Everything else (memberships, payment collection on-site, advanced analytics dashboards) = Phase 2.

---

# 4) Folder structures (clean, opinionated)

## Frontend (public) — `brownpatience-frontend/`

```
brownpatience-frontend/
├─ public/                # static assets, robots.txt, favicon
├─ src/
│  ├─ main.jsx
│  ├─ index.css           # Tailwind entry + global styles (reduced-motion class)
│  ├─ App.jsx
│  ├─ pages/
│  │  ├─ Home/
│  │  │  ├─ Home.jsx
│  │  │  └─ Home.module.css
│  │  ├─ About.jsx
│  │  ├─ Books/
│  │  │  ├─ BooksList.jsx
│  │  │  └─ BookDetail.jsx
│  │  ├─ Blog/
│  │  │  ├─ BlogList.jsx
│  │  │  └─ PostDetail.jsx
│  │  ├─ Subscriptions.jsx
│  │  ├─ Community.jsx
│  │  └─ Contact.jsx
│  ├─ components/
│  │  ├─ ui/              # Radix + small presentational components (Button, Modal, Tooltip)
│  │  ├─ layout/          # Header, Footer, SkipLink
│  │  ├─ hero/
│  │  ├─ books/
│  │  ├─ blog/
│  │  └─ newsletter/      # Newsletter form component
│  ├─ hooks/              # usePrefersReducedMotion, useFetch, useIntersection
│  ├─ services/
│  │  ├─ firebase.js      # read-only Firestore client reads
│  │  ├─ api.js           # wrappers for /api endpoints (subscribe, excerpt)
│  │  └─ cloudinary.js    # helper to build Cloudinary urls
│  ├─ lib/
│  │  └─ analytics.js
│  └─ styles/
├─ tailwind.config.cjs
├─ vite.config.js
├─ README.md
```

## Admin — `brownpatience-admin/`

```
brownpatience-admin/
├─ public/
├─ src/
│  ├─ main.jsx
│  ├─ index.css
│  ├─ App.jsx
│  ├─ pages/
│  │  ├─ DashboardHome.jsx
│  │  ├─ BooksManager/
│  │  │  ├─ BooksList.jsx
│  │  │  └─ BookEditor.jsx
│  │  ├─ PostsManager/
│  │  ├─ TestimonialsManager/
│  │  ├─ MessagesInbox.jsx
│  │  ├─ Subscribers.jsx
│  │  └─ CommentsModeration.jsx
│  ├─ components/
│  │  ├─ AdminHeader.jsx
│  │  └─ MediaPicker.jsx   # Cloudinary integration
│  ├─ services/
│  │  ├─ firebase-admin-client.js  # firebase client for admin actions (use admin claim checks)
│  │  └─ api.js           # calls to Cloud Functions
│  └─ utils/
├─ tailwind.config.cjs
├─ vite.config.js
├─ README.md
```

## Functions — `brownpatience-functions/`

```
brownpatience-functions/
├─ package.json
├─ src/
│  ├─ index.ts
│  ├─ handlers/
│  │  ├─ subscribe.ts        # POST /subscribe: verify recaptcha, write to subscribers
│  │  ├─ submitMessage.ts    # POST /message: verify recaptcha, write messages, send email
│  │  ├─ verifyRecaptcha.ts  # helper
│  │  ├─ approveComment.ts   # admin-only callable fn (could be triggered from admin)
│  │  └─ sendEmail.ts        # email sending helper (nodemailer or SendGrid)
│  └─ utils/
├─ .env (local for dev; use firebase functions:config for prod)
└─ README.md
```

## Infra docs — `infra/` (optional)

```
infra/
├─ github-actions/          # sample deploy/workflow files
├─ dns-setup.md
├─ cpanel-deploy.sh         # optional FTP upload script
└─ README.md
```

---

# 5) Key components & pages (brief)

- Header (sticky + skiplink)
- Hero (left text, right Lottie or illustration; CTAs)
- FeaturedBooksCarousel (desktop grid, mobile horizontal scroll)
- NewsletterForm (email + name -> `/api/subscribe`)
- BlogList & PostDetail (with comments component)
- TestimonialsCarousel
- Footer (reduced-motion toggle)

Admin: BooksManager, PostsManager (rich text markdown editor), Subscribers list, Messages inbox, CommentsModeration, MediaPicker.

---

# 6) Data model (Firestore) — quick reference

Collections: `books`, `posts`, `testimonials`, `messages`, `comments`, `likes`, `subscribers`, `siteSettings`, `mediaRecords`.

Sample minimal `subscribers` document:

```
{
  email: string,
  name?: string,
  source: 'homepage'|'footer'|'bookPage',
  utm?: {...},
  createdAt: timestamp,
  ipHash: string,
  verified: false
}
```

Comments: include `status: 'pending'|'approved'|'deleted'`, `postId`, `parentId`, `content`, `createdAt`.

---

# 7) Backend endpoints needed (Cloud Functions)

- `POST /subscribe` — verify recaptcha → write to `subscribers`. Rate-limit. Return success.
- `POST /message` — verify recaptcha → write to `messages` (type booking or whatsapp contact) → send notification email to admin.
- `POST /comment` — verify recaptcha → basic profanity check → write `comments` with `status: 'pending'`.
- `GET /book-excerpt?bookId=...` — return excerpt HTML for modal.
- `POST /admin/approveComment` — callable/admin-only to set comment to `approved`.

Implementation note: prefer HTTPS callable functions for admin-only operations and HTTP POSTs for public submissions that require recaptcha verification server-side.

---

# 8) Firestore security & rules (high level)

- Public reads allowed for `books`, `posts` (published), `testimonials` (approved), `siteSettings`.
- Writes from public only allowed to `messages` and `comments` **via Cloud Function verification** — i.e., block direct client writes to those collections or require a server-issued write token.
- Admin-only writes (CRUD on `books`, `posts`, `testimonials`) require `auth.token.admin === true` custom claim.
- Use `validate` rules to ensure required fields and sanitize length limits.

(Implement server-side recaptcha verification — do not trust client recaptcha tokens alone.)

---

# 9) Media (Cloudinary) integration rules

- Admin uploads images through admin UI -> upload signed via server function OR signed upload tokens. Store returned `public_id` and secure URL in `mediaRecords`.
- Use Cloudinary transforms for multiple breakpoints (320, 640, 960, 1280).
- Use progressive or automatic format selection (f_auto, q_auto) and `loading="lazy"`.

---

# 10) Authentication & admin account flow

- Admin accounts are created manually in Firebase Console. No public self-registration.
- Use Firebase Auth (email/password). Assign `admin` custom claim in Firebase console or via Admin SDK to the admin user(s).
- On admin client, check custom claim before allowing access to admin routes; verify with a backend callable function when needed.

---

# 11) UI/UX & animation rules (developer constraints)

- Respect `prefers-reduced-motion`.
- Use Animate.css for on-scroll reveals; use IntersectionObserver to trigger reveal classes.
- Use react-spring for hover physics and small parallax; avoid GSAP (heavy).
- Lottie only for hero or one micro-illustration; lazy-load and ensure fallback static SVG.
- Keep JS animation payload low; prefer CSS transitions for simple effects.

---

# 12) Dev workflow / recommended commands

- Use `pnpm` or `yarn` for consistent installs.
- Local dev: `pnpm dev` (both frontend and admin).
- Build: `pnpm build`.
- Deploy functions: `firebase deploy --only functions`.
- Deploy admin: push to GitHub -> Vercel auto-deploy. Set env vars in Vercel dashboard.
- Deploy frontend: build and upload `dist/` to cPanel (FTP or Git deploy).

---

# 13) CI/CD (suggested GitHub Actions)

- Admin repo: on push `main` -> run tests -> build -> deploy to Vercel (Vercel integration recommended).
- Frontend repo: on `main` -> build -> run `ftp-deploy` action to WhoGhost cPanel or create artifact and manual FTP.
- Functions repo: on `main` -> `firebase-tools` deploy to functions (secure the CI using Firebase service account).

(Include secrets in GitHub repo secrets: FIREBASE_TOKEN, VERCEL_TOKEN, CPANEL_FTP creds only if needed.)

---

# 14) Testing & QA checklist (must pass before demo)

- Visual: responsive checks at common breakpoints, hero layout intact.
- Accessibility: keyboard nav, labels for forms, `alt` on images, contrast checks.
- Performance: Lighthouse basic: mobile >= 70, desktop >= 90.
- Security: recaptcha verification works, rate-limits working server-side.
- Functional:

  - Subscribe form submits -> Firestore `subscribers` created.
  - Book card buy link opens in new tab with UTM.
  - Admin books CRUD works; image upload stores to Cloudinary.
  - Messages store to `messages` and admin gets email + unread counter increments.
  - Comments created as `pending` and admin can approve -> becomes visible.

- Edge cases: invalid email, recaptcha fail, Cloudinary errors gracefully handled.

---

# 15) Acceptance criteria (final)

- Public site deployed; admin dashboard deployed and accessible only by Firebase-created admin.
- Subscriber flow writes to Firestore; admin can view and filter subscribers.
- Book showcase displays; buy links open in new tab.
- Blog posts publishable from admin; comments moderated by admin.
- Messages (booking) stored and trigger admin notification email + red unread indicator.

---

# 16) Sprint plan (recommended)

- Sprint 0 (1 day): Repo scaffolding, Firebase project, Cloudinary account, environment setup.
- Sprint 1 (3–5 days): Homepage + featured books + newsletter form wired to `/subscribe` function.
- Sprint 2 (4–6 days): Admin skeleton, auth, books CRUD + Cloudinary upload.
- Sprint 3 (4–6 days): Blog CRUD + public blog + comment submission + moderation UI.
- Sprint 4 (2–3 days): Messages form + email notifications + unread counter + QA + deploy front & admin.
- Sprint 5 (2 days): Performance & accessibility polish, handover docs.

---

# 17) Developer conventions & tooling

- Use Prettier + ESLint (React rules) and Husky pre-commit.
- Commit messages: `feat:`, `fix:`, `chore:`, `docs:`.
- Branching: feature branches off `develop` (or `main` if small team), PR reviews required.
- Document environment variables in `.env.example` and keep secrets out of repo.

---

# 18) Deliverables & handover

- Two repos (`frontend`, `admin`) and functions repo with README for setup and deploy.
- `infra/` doc with DNS and cPanel steps (how to set A/CNAME records).
- Handover doc: where to find env vars, how to create admin in Firebase, how to rotate Cloudinary keys, how to change hero text/images.
- Minimal operational doc for client: how to add a book, publish a post, approve comments, and view subscribers.

---

# 19) Important gotchas & tips

- Don’t allow direct client writes to production `comments` or `messages` — always verify recaptcha server-side before writing.
- For lightness: prefer Cloudinary transforms over multiple image files stored in repo.
- Keep Lottie usage to a minimum — they can blow up payload. Prefer animated SVGs for tiny decorative icons.
- Admin accounts will be created only via Firebase Console (as requested). Build helpful admin docs for that manual step.

---

# 20) Minimal example of `POST /subscribe` flow (pseudo)

- Client: POST `/api/subscribe` with `{ email, name, recaptchaToken }`.
- Server: verify token with Google reCAPTCHA -> if valid, write to Firestore `subscribers` with `createdAt` and `ipHash` -> return 200.
- Client: show success toast and clear form.
