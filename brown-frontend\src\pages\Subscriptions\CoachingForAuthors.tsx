import { useInView } from "react-intersection-observer";
import { useSpring, animated } from "@react-spring/web";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Check,
  MessageCircle,
  BookOpen,
  Users,
  Target,
  AlertTriangle,
} from "lucide-react";

const CoachingForAuthors = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [contentRef, contentInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [processRef, processInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const contentSpring = useSpring({
    opacity: contentInView ? 1 : 0,
    transform: contentInView ? "translateY(0px)" : "translateY(30px)",
  });

  const processSpring = useSpring({
    opacity: processInView ? 1 : 0,
    transform: processInView ? "translateY(0px)" : "translateY(30px)",
  });

  const handleWhatsAppContact = () => {
    const message = encodeURIComponent(
      "Hi! I'm interested in the Coaching for Authors package. Could you please provide more details?"
    );
    const whatsappUrl = `https://wa.me/message/DOCQNYXAEPVDH1?text=${message}`;
    window.open(whatsappUrl, "_blank", "noopener,noreferrer");
  };

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Hero Section */}
      <animated.section
        ref={heroRef}
        style={heroSpring}
        className="py-20 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-serif text-brown-900 mb-6">
            Coaching For Authors
          </h1>
          <h2 className="text-xl md:text-2xl text-brown-700 mb-8 leading-relaxed">
            Struggling to put your book together? Let's do it chapter by
            chapter, you and I.
          </h2>
          <Button
            onClick={handleWhatsAppContact}
            size="lg"
            className="bg-brown-800 hover:bg-brown-900 text-white px-8 py-3"
          >
            <MessageCircle className="w-5 h-5 mr-2" />
            Contact Me
          </Button>
        </div>
      </animated.section>

      {/* Main Content */}
      <animated.section
        ref={contentRef}
        style={contentSpring}
        className="py-16 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-start">
            {/* Left Column - Main Description */}
            <div className="space-y-8">
              <Card className="p-6">
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-xl font-serif font-semibold text-brown-900 mb-3 flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      What's this about?
                    </h3>
                    <p className="text-brown-700 leading-relaxed">
                      This package is where I get to coach you stage by stage on
                      how to write your book. You'll be gaining two things:
                      you'll be learning to write better while authoring your
                      book at the same time. I'm an author, ghostwriter, editor,
                      and a writing coach. These mean I know how to write. I
                      know how to write for people. I know how to enrich what
                      people have written. And I sure know how to help you write
                      (by yourself!) what you need to write.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-serif font-semibold text-brown-900 mb-3 flex items-center gap-2">
                      <BookOpen className="w-5 h-5" />
                      Where do we begin?
                    </h3>
                    <p className="text-brown-700 leading-relaxed mb-4">
                      Every book on the earth has a purpose. Yours does too. And
                      that's where we'll begin — by defining your book's
                      purpose, zeroing in on its audience, and answering the big
                      question: Why? Why are you writing this book? What do you
                      want your reader to take away from it?
                    </p>
                    <ul className="space-y-2 text-brown-700">
                      <li className="flex items-start gap-2">
                        <Check className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                        Then we brainstorm on the chapters.
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                        What will Chapter One say?
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                        What will Chapter Two contain?
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                        Should the book be broken into parts or sections?
                      </li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Button
                onClick={handleWhatsAppContact}
                size="lg"
                className="w-full bg-brown-800 hover:bg-brown-900 text-white"
              >
                <MessageCircle className="w-5 h-5 mr-2" />
                Subscribe
              </Button>
            </div>

            {/* Right Column - Vision & Purpose */}
            <div className="space-y-6">
              <Card>
                <CardContent className="p-6">
                  <p className="text-brown-700 leading-relaxed mb-4">
                    That, you see, is what this Coaching for Authors package is
                    about. I'll be the person that helps you share your message,
                    the one that helps you tell your story — so your readers
                    will hear precisely what you've been wanting them to hear,
                    the message that's been burning in your heart.
                  </p>
                  <p className="text-brown-700 leading-relaxed">
                    I got this vision from an author named Luke. The guy was a
                    meticulous physician who brought that very care to his
                    writing. Right from the start of the book, he stated its
                    purpose. His reader was named Theophilus. And this author
                    wanted Theophilus to know the truth. So he wrote chapter
                    after chapter, explaining in gripping detail, employing the
                    kind of storytelling that renders you immersed. Everything
                    drove towards that goal — so Theophilus would know the
                    truth. Centuries later, we're also learning the truth from
                    the book Luke wrote.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <p className="text-brown-700 leading-relaxed mb-4">
                    That's what books do; they embody truth. In the kind of
                    format that lasts forever. Long after the author has
                    breathed their last, the truth lives on. Do you know why
                    you're so drawn to write this book? Because people need this
                    truth—the very one beating in your chest; the one making you
                    interested in this package.
                  </p>
                  <p className="text-brown-700 leading-relaxed">
                    It's also why I'll help you. You're Luke, writing to your
                    Theophilus. I'll be your helpmeet. I'll be the one who
                    ensures Theophilus hears precisely what Luke wants to say.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </animated.section>

      {/* Process Section */}
      <animated.section
        ref={processRef}
        style={processSpring}
        className="py-16 px-4 sm:px-6 lg:px-8 bg-white"
      >
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-12 text-center">
            What's the modality of this coaching? How do we do it?
          </h2>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-serif text-brown-900 flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Step 1: Discovery & Planning
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-brown-700 leading-relaxed mb-4">
                  We start by talking about the Big W's: Why are you writing
                  this book? Where did the drive to write it come from? Who are
                  you writing to? What do you want your reader to take away from
                  it? We could do this over a call, emails, or via chats.
                </p>
                <p className="text-brown-700 leading-relaxed">
                  There are H questions too: How far have you gone with the book
                  or the planning of the book? How best do you think I can help
                  you through this writing phase?
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-serif text-brown-900 flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  Step 2: Outline Creation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-brown-700 leading-relaxed">
                  After I've understood your passion for the book, you and I
                  will draw out an outline that works with your schedule (to be
                  frank, you might need to make your schedule work with the
                  outline or you may never find the time).
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-serif text-brown-900 flex items-center gap-2">
                  <Check className="w-5 h-5" />
                  Step 3: Writing & Feedback
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-brown-700 leading-relaxed">
                  You begin writing. And you send a chapter (or more) a week. As
                  you send me the chapters, we talk about it. I tell you what I
                  noticed, if it's all good or how much better it can get, how
                  you can modify things, how to stick to appropriate tenses,
                  punctuations, where a subtopic is necessary, where an
                  illustration would aid understanding, and the repeated things
                  that must be cut out.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-serif text-brown-900 flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Step 4: Completion
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-brown-700 leading-relaxed">
                  As your editor, I would have done these for you, but this
                  Coaching package ensures you learn to do them yourself, so you
                  can grow more confident in your writing and attack your
                  subsequent writing or books with experience and more
                  knowledge. Bit by bit, as sure as the dawn, you'll complete
                  your book.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Warning Section */}
          <Card className="border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="text-xl font-serif text-orange-900 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                WARNING!!!
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-orange-800 leading-relaxed mb-4">
                As you write, you may find your book taking a different, deeper
                turn. You may find a certain point becoming so important that
                you keep expanding on it. Thing is: When you 'start' writing,
                beautiful things happen. And I'll fan the flame; I'll encourage
                you to ease up on the outline and allow yourself just write.
                Thoughts, when they're given expression, grow. You'll be amazed
                at the things flowing out of you.
              </p>
              <p className="text-orange-800 leading-relaxed">
                But no, we won't allow wordiness dilute the message. Unnecessary
                things will be mercilessly shown the door. There is a time to
                use a comma; there is a time to put a full stop. I'll be your
                guide.
              </p>
            </CardContent>
          </Card>
        </div>
      </animated.section>

      {/* Pricing Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-cream-50">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-12 text-center">
            What's payment like?
          </h2>

          <Card className="relative overflow-hidden border-brown-300 shadow-lg max-w-2xl mx-auto">
            <div className="absolute top-0 left-0 right-0 bg-brown-800 text-white text-center py-2 text-sm font-medium">
              Monthly Coaching
            </div>
            <CardHeader className="text-center pb-8 pt-12">
              <CardTitle className="text-2xl font-serif text-brown-900">
                Coaching for Authors
              </CardTitle>
              <div className="text-4xl font-bold text-brown-800 mt-4">
                ₦25,000
                <span className="text-lg font-normal text-brown-600">
                  /month
                </span>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-600" />
                  <span className="text-brown-700">
                    No limit on chapters per month
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-600" />
                  <span className="text-brown-700">
                    Stage-by-stage coaching
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-600" />
                  <span className="text-brown-700">
                    Comprehensive feedback on each chapter
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-600" />
                  <span className="text-brown-700">
                    Book structure & outline development
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-600" />
                  <span className="text-brown-700">
                    Writing skills development
                  </span>
                </div>
              </div>

              <div className="bg-brown-50 p-4 rounded-lg mt-6">
                <p className="text-brown-700 text-sm leading-relaxed">
                  <strong>Motivation:</strong> You may send in a chapter a week,
                  or send in three chapters a week and get my feedback. But once
                  a month is over, you'll have to renew your subscription for
                  the new month. It's like this because I want you to be
                  encouraged to start and not slow down. If you know your
                  monthly subscription is counting, it should motivate you to
                  get on with the book. Why let it take ten months when it could
                  have taken three?
                </p>
              </div>

              <Button
                onClick={handleWhatsAppContact}
                className="w-full bg-brown-800 hover:bg-brown-900 text-white mt-6"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Subscribe Now
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-12 text-center">
            Frequently Asked Questions
          </h2>
          <div className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  Can we start right now?
                </h3>
                <p className="text-brown-700">
                  Of course. Send a message to subscribe. Then we can start with
                  those W questions: Why? Why is this book necessary? What will
                  it say?
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  How many chapters can I submit per month?
                </h3>
                <p className="text-brown-700">
                  There's no limit on chapters. You may send in a chapter a
                  week, or send in three chapters a week and get feedback. The
                  goal is to keep you motivated and moving forward with your
                  book.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  What if my book takes a different direction while writing?
                </h3>
                <p className="text-brown-700">
                  That's perfectly normal and often beautiful! When you start
                  writing, thoughts grow and develop. I'll encourage you to
                  follow the natural flow while ensuring the message stays clear
                  and focused.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  How do we communicate during the coaching process?
                </h3>
                <p className="text-brown-700">
                  We can communicate through calls, emails, or chats - whatever
                  works best for you. The important thing is maintaining regular
                  communication as you progress through your book.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  What's the difference between this and editing services?
                </h3>
                <p className="text-brown-700">
                  This coaching package teaches you to write better while
                  creating your book. Instead of me editing your finished work,
                  I guide you to write it well from the start, building your
                  skills for future projects.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-cream-50">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-8 text-center">
            Ask Your Questions
          </h2>
          <Card>
            <CardContent className="p-6">
              <form className="space-y-6">
                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-brown-900 mb-2"
                  >
                    Your Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-brown-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-brown-900 mb-2"
                  >
                    Your Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    placeholder="Name"
                    className="w-full px-3 py-2 border border-brown-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
                <div>
                  <label
                    htmlFor="message"
                    className="block text-sm font-medium text-brown-900 mb-2"
                  >
                    Your Message
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    placeholder="Leave a comment..."
                    className="w-full px-3 py-2 border border-brown-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  ></textarea>
                </div>
                <Button
                  type="submit"
                  className="w-full bg-brown-800 hover:bg-brown-900 text-white"
                >
                  Send Message
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
};

export default CoachingForAuthors;
