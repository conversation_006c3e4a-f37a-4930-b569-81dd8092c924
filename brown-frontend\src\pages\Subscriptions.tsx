import { useInView } from 'react-intersection-observer';
import { useSpring, animated } from '@react-spring/web';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, MessageCircle, Mail } from 'lucide-react';

const Subscriptions = () => {
  const [heroRef, heroInView] = useInView({ threshold: 0.1, triggerOnce: true });
  const [tiersRef, tiersInView] = useInView({ threshold: 0.1, triggerOnce: true });

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? 'translateY(0px)' : 'translateY(50px)',
  });

  const tiersSpring = useSpring({
    opacity: tiersInView ? 1 : 0,
    transform: tiersInView ? 'translateY(0px)' : 'translateY(30px)',
  });

  const handleWhatsAppSubscription = (tier: string) => {
    const message = encodeURIComponent(
      `Hi <PERSON>! I'm interested in the ${tier} subscription. Could you please provide more details about pricing and how to get started?`
    );
    const whatsappUrl = `https://wa.me/1234567890?text=${message}`;
    window.open(whatsappUrl, '_blank', 'noopener,noreferrer');
  };

  const handleContactForPaid = () => {
    window.open('/contact', '_self');
  };

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Hero Section */}
      <animated.section 
        ref={heroRef}
        style={heroSpring}
        className="py-20 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-serif text-brown-900 mb-6">
            Writing Subscriptions
          </h1>
          <p className="text-xl text-brown-700 max-w-2xl mx-auto leading-relaxed">
            Join a community of writers committed to growth, learning, and sharing their stories with the world.
          </p>
        </div>
      </animated.section>

      {/* Subscription Tiers */}
      <animated.section 
        ref={tiersRef}
        style={tiersSpring}
        className="py-16 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8">
            {/* Free Tier */}
            <Card className="relative overflow-hidden">
              <CardHeader className="text-center pb-8">
                <Badge variant="secondary" className="w-fit mx-auto mb-4">
                  Free
                </Badge>
                <CardTitle className="text-2xl font-serif text-brown-900">
                  Newsletter Subscriber
                </CardTitle>
                <div className="text-3xl font-bold text-brown-800 mt-4">
                  $0<span className="text-lg font-normal text-brown-600">/month</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Weekly writing tips</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Book recommendations</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Writing prompts</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Early access to blog posts</span>
                  </div>
                </div>
                <Button 
                  onClick={() => handleWhatsAppSubscription('Newsletter')}
                  className="w-full bg-brown-800 hover:bg-brown-900 text-white mt-6"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Subscribe Free
                </Button>
              </CardContent>
            </Card>

            {/* Premium Tier */}
            <Card className="relative overflow-hidden border-brown-300 shadow-lg">
              <div className="absolute top-0 left-0 right-0 bg-brown-800 text-white text-center py-2 text-sm font-medium">
                Most Popular
              </div>
              <CardHeader className="text-center pb-8 pt-12">
                <Badge variant="default" className="w-fit mx-auto mb-4 bg-brown-800">
                  Premium
                </Badge>
                <CardTitle className="text-2xl font-serif text-brown-900">
                  Writing Circle
                </CardTitle>
                <div className="text-3xl font-bold text-brown-800 mt-4">
                  Contact<span className="text-lg font-normal text-brown-600"> for pricing</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Everything in Newsletter</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Monthly group coaching calls</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Exclusive writing challenges</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Peer feedback community</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Resource library access</span>
                  </div>
                </div>
                <Button 
                  onClick={() => handleWhatsAppSubscription('Writing Circle')}
                  className="w-full bg-brown-800 hover:bg-brown-900 text-white mt-6"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Get Subscription
                </Button>
              </CardContent>
            </Card>

            {/* VIP Tier */}
            <Card className="relative overflow-hidden">
              <CardHeader className="text-center pb-8">
                <Badge variant="outline" className="w-fit mx-auto mb-4 border-brown-300">
                  VIP
                </Badge>
                <CardTitle className="text-2xl font-serif text-brown-900">
                  Personal Mentorship
                </CardTitle>
                <div className="text-3xl font-bold text-brown-800 mt-4">
                  Contact<span className="text-lg font-normal text-brown-600"> for pricing</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Everything in Writing Circle</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">1-on-1 monthly sessions</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Manuscript reviews</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Publishing guidance</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-600" />
                    <span className="text-brown-700">Direct WhatsApp access</span>
                  </div>
                </div>
                <Button 
                  onClick={handleContactForPaid}
                  variant="outline"
                  className="w-full border-brown-300 text-brown-700 hover:bg-brown-50 mt-6"
                >
                  Contact for Details
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </animated.section>

      {/* FAQ Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-12 text-center">
            Frequently Asked Questions
          </h2>
          <div className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  How do I cancel my subscription?
                </h3>
                <p className="text-brown-700">
                  You can cancel anytime by contacting us via WhatsApp or email. There are no long-term commitments.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  What if I'm not satisfied with my subscription?
                </h3>
                <p className="text-brown-700">
                  We offer a 30-day money-back guarantee for all paid subscriptions. Your satisfaction is our priority.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-brown-900 mb-2">
                  Can I upgrade or downgrade my subscription?
                </h3>
                <p className="text-brown-700">
                  Yes! You can change your subscription tier at any time. Contact us and we'll help you make the transition.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Subscriptions;