import { useState, useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { useSpring, animated } from '@react-spring/web';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Clock, Calendar } from 'lucide-react';

// Mock data - replace with Firestore data
const mockPosts = [
  {
    id: '1',
    slug: 'finding-your-writing-voice',
    title: 'Finding Your Writing Voice: A Journey of Self-Discovery',
    excerpt: 'Every writer has a unique voice waiting to be discovered. Learn how to uncover yours and let it shine through your writing.',
    featuredImage: 'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=800&h=400&fit=crop',
    publishDate: '2024-01-15',
    readTime: '5 min read',
    tags: ['Writing Tips', 'Voice'],
    status: 'published'
  },
  {
    id: '2',
    slug: 'overcoming-writers-block',
    title: 'Overcoming Writer\'s Block: Practical Strategies That Work',
    excerpt: 'Writer\'s block doesn\'t have to be the end of your creative journey. Discover proven techniques to break through and keep writing.',
    featuredImage: 'https://images.unsplash.com/photo-1471107340929-a87cd0f5b5f3?w=800&h=400&fit=crop',
    publishDate: '2024-01-10',
    readTime: '7 min read',
    tags: ['Writing Tips', 'Productivity'],
    status: 'published'
  },
  {
    id: '3',
    slug: 'storytelling-fundamentals',
    title: 'The Fundamentals of Great Storytelling',
    excerpt: 'Master the essential elements that make stories compelling and memorable. From character development to plot structure.',
    featuredImage: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&h=400&fit=crop',
    publishDate: '2024-01-05',
    readTime: '10 min read',
    tags: ['Storytelling', 'Craft'],
    status: 'published'
  }
];

const BlogList = () => {
  const [posts, setPosts] = useState(mockPosts);
  const [filteredPosts, setFilteredPosts] = useState(mockPosts);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState('all');

  const [heroRef, heroInView] = useInView({ threshold: 0.1, triggerOnce: true });
  const [postsRef, postsInView] = useInView({ threshold: 0.1, triggerOnce: true });

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? 'translateY(0px)' : 'translateY(50px)',
  });

  const postsSpring = useSpring({
    opacity: postsInView ? 1 : 0,
    transform: postsInView ? 'translateY(0px)' : 'translateY(30px)',
  });

  // Get all unique tags
  const allTags = [...new Set(posts.flatMap(post => post.tags))];

  // Filter posts based on search and tag
  useEffect(() => {
    let filtered = posts.filter(post => post.status === 'published');

    if (searchTerm) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedTag !== 'all') {
      filtered = filtered.filter(post => post.tags.includes(selectedTag));
    }

    setFilteredPosts(filtered);
  }, [searchTerm, selectedTag, posts]);

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Hero Section */}
      <animated.section 
        ref={heroRef}
        style={heroSpring}
        className="py-20 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-serif text-brown-900 mb-6">
            Writing Blog
          </h1>
          <p className="text-xl text-brown-700 max-w-2xl mx-auto leading-relaxed">
            Insights, tips, and inspiration for writers at every stage of their journey. 
            From craft techniques to publishing advice, find everything you need to grow as a writer.
          </p>
        </div>
      </animated.section>

      {/* Filters */}
      <section className="py-8 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <div className="flex-1">
              <Input
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="md:w-48">
              <Select value={selectedTag} onValueChange={setSelectedTag}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by tag" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tags</SelectItem>
                  {allTags.map(tag => (
                    <SelectItem key={tag} value={tag}>{tag}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </section>

      {/* Posts Grid */}
      <animated.section 
        ref={postsRef}
        style={postsSpring}
        className="py-16 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map((post) => (
              <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow group">
                <div className="aspect-[16/9] overflow-hidden">
                  <img
                    src={post.featuredImage}
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    loading="lazy"
                  />
                </div>
                <CardContent className="p-6">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {post.tags.map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <h3 className="text-xl font-serif text-brown-900 mb-3 group-hover:text-brown-700 transition-colors line-clamp-2">
                    <Link to={`/blog/${post.slug}`}>
                      {post.title}
                    </Link>
                  </h3>
                  <p className="text-brown-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm text-brown-500">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(post.publishDate).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {post.readTime}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredPosts.length === 0 && (
            <div className="text-center py-12">
              <p className="text-brown-600 text-lg">No articles found matching your criteria.</p>
            </div>
          )}
        </div>
      </animated.section>
    </div>
  );
};

export default BlogList;