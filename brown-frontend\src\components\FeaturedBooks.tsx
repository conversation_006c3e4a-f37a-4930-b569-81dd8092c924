import { useSpring, animated } from '@react-spring/web';
import { useInView } from 'react-intersection-observer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { ExternalLink, BookOpen, Calendar } from 'lucide-react';
import booksCollection from '@/assets/books-collection.jpg';

interface Book {
  id: string;
  title: string;
  cover: string;
  blurb: string;
  publishDate: string;
  tags: string[];
  buyUrl: string;
  excerpt?: string;
}

const books: Book[] = [
  {
    id: '1',
    title: 'The Writer\'s Journey',
    cover: booksCollection,
    blurb: 'A comprehensive guide to crafting compelling narratives that captivate readers from start to finish.',
    publishDate: '2023',
    tags: ['Writing Craft', 'Storytelling'],
    buyUrl: 'https://amazon.com/writers-journey',
    excerpt: 'Every story begins with a single word, but great stories begin with courage...'
  },
  {
    id: '2', 
    title: 'Publishing Mastery',
    cover: booksCollection,
    blurb: 'Navigate the modern publishing landscape with confidence and strategic insight.',
    publishDate: '2022',
    tags: ['Publishing', 'Business'],
    buyUrl: 'https://amazon.com/publishing-mastery',
    excerpt: 'The publishing world has changed dramatically in the past decade...'
  },
  {
    id: '3',
    title: 'Creative Courage',
    cover: booksCollection,
    blurb: 'Overcome creative blocks and unlock your authentic voice as a writer.',
    publishDate: '2021',
    tags: ['Creativity', 'Mindset'],
    buyUrl: 'https://amazon.com/creative-courage',
    excerpt: 'Fear is the greatest enemy of creativity, but it doesn\'t have to be...'
  }
];

const FeaturedBooks = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? 'translateY(0px)' : 'translateY(50px)',
  });

  const trackBookClick = (bookId: string, title: string) => {
    // Analytics tracking would go here
    console.log('Book click tracked:', { bookId, title });
  };

  return (
    <section id="books" ref={ref} className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <BookOpen className="w-4 h-4" />
              Featured Publications
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Books That Inspire & Guide
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              Each book represents years of experience distilled into practical wisdom for writers at every stage of their journey.
            </p>
          </div>

          {/* Books Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {books.map((book, index) => {
              const cardSpring = useSpring({
                opacity: inView ? 1 : 0,
                transform: inView ? 'translateY(0px)' : 'translateY(50px)',
                delay: inView ? 200 + index * 100 : 0,
              });

              return (
                <animated.div
                  key={book.id}
                  style={cardSpring}
                >
                  <Card className="group h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 interactive-lift">
                    <CardHeader className="p-0">
                      <div className="aspect-[3/4] rounded-t-lg overflow-hidden">
                        <img
                          src={book.cover}
                          alt={`Cover of ${book.title}`}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                          loading="lazy"
                        />
                      </div>
                    </CardHeader>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Title & Date */}
                        <div>
                          <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                            {book.title}
                          </h3>
                          <div className="flex items-center gap-1 text-sm text-brand-secondary/60">
                            <Calendar className="w-4 h-4" />
                            Published {book.publishDate}
                          </div>
                        </div>

                        {/* Blurb */}
                        <p className="text-brand-secondary/80 leading-relaxed">
                          {book.blurb}
                        </p>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-2">
                          {book.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-3 py-1 bg-brand-accent/10 text-brand-accent text-xs font-medium rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        {/* Actions */}
                        <div className="flex gap-2 pt-2">
                          <Button
                            variant="hero"
                            size="sm"
                            className="flex-1"
                            onClick={() => {
                              trackBookClick(book.id, book.title);
                              window.open(book.buyUrl, '_blank', 'noopener,noreferrer');
                            }}
                          >
                            <ExternalLink className="w-4 h-4 mr-1" />
                            Buy Book
                          </Button>
                          {book.excerpt && (
                            <Button
                              variant="outline"
                              size="sm"
                            >
                              Preview
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </animated.div>
              );
            })}
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default FeaturedBooks;