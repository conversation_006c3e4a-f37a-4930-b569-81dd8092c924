import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import Header from "./layout/Header";
import Footer from "./layout/Footer";
import Home from "./pages/Home";
import About from "./pages/About";
import BooksList from "./pages/Books/BooksList";
import BookDetail from "./pages/Books/BookDetail";
import BlogList from "./pages/Blog/BlogList";
import Subscriptions from "./pages/Subscriptions";
import Community from "./pages/Community";
import GuidanceForSolopreneurs from "./pages/Subscriptions/GuidanceForSolopreneurs";
import CoachingForAuthors from "./pages/Subscriptions/CoachingForAuthors";
import {
  BookWritingEditing,
  CoachingForWriters,
  ContentWriting,
} from "./pages/Services";

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-cream-50">
        <Header />
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/books" element={<BooksList />} />
            <Route path="/books/:slug" element={<BookDetail />} />
            <Route path="/blog" element={<BlogList />} />
            <Route path="/subscriptions" element={<Subscriptions />} />
            <Route
              path="/subscriptions/guidance-for-solopreneurs"
              element={<GuidanceForSolopreneurs />}
            />
            <Route
              path="/subscriptions/coaching-for-authors"
              element={<CoachingForAuthors />}
            />
            <Route path="/community" element={<Community />} />

            {/* Service Pages */}
            <Route
              path="/services/book-writing-editing"
              element={<BookWritingEditing />}
            />
            <Route
              path="/services/coaching-for-writers"
              element={<CoachingForWriters />}
            />
            <Route
              path="/services/content-writing"
              element={<ContentWriting />}
            />

            {/* <Route path="/contact" element={<Contact />} /> */}
          </Routes>
        </main>
        <Footer />
        <Toaster />
      </div>
    </Router>
  );
}

export default App;
