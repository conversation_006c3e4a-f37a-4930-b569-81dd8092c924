import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import {
  <PERSON>R<PERSON>,
  BookOpen,
  Users,
  PenTool,
  MessageCircle,
  FileText,
  Video,
} from "lucide-react";

interface Service {
  id: string;
  title: string;
  description: string;
  features: string[];
  icon: React.ComponentType<{ className?: string }>;
  price: string;
  duration: string;
  ctaText: string;
  popular?: boolean;
}

const services: Service[] = [
  {
    id: "1",
    title: "One-on-One Coaching",
    description:
      "Personalized guidance to help you overcome writing blocks and develop your unique voice.",
    features: [
      "Weekly 60-minute sessions",
      "Personalized writing exercises",
      "Manuscript feedback & editing",
      "Publishing strategy guidance",
      "Email support between sessions",
    ],
    icon: Users,
    price: "$150",
    duration: "per session",
    ctaText: "Book Session",
    popular: true,
  },
  {
    id: "2",
    title: "Writing Workshops",
    description:
      "Interactive group sessions focusing on specific aspects of the writing craft.",
    features: [
      "Small group environment (8-12 writers)",
      "Genre-specific workshops",
      "Live writing exercises",
      "Peer feedback sessions",
      "Take-home resources",
    ],
    icon: PenTool,
    price: "$75",
    duration: "per workshop",
    ctaText: "Join Workshop",
  },
  {
    id: "3",
    title: "Manuscript Review",
    description:
      "Comprehensive feedback on your completed manuscript with detailed improvement suggestions.",
    features: [
      "In-depth structural analysis",
      "Character development feedback",
      "Plot & pacing evaluation",
      "Line-by-line editing suggestions",
      "2-hour feedback call included",
    ],
    icon: FileText,
    price: "$500",
    duration: "per manuscript",
    ctaText: "Submit Manuscript",
  },
  {
    id: "4",
    title: "Publishing Mentorship",
    description:
      "Navigate the publishing landscape with expert guidance from query letters to book launches.",
    features: [
      "Publishing strategy development",
      "Query letter optimization",
      "Agent/publisher research",
      "Contract negotiation support",
      "Marketing & promotion guidance",
    ],
    icon: BookOpen,
    price: "$200",
    duration: "per month",
    ctaText: "Start Mentorship",
  },
  {
    id: "5",
    title: "Online Writing Course",
    description:
      "Self-paced comprehensive course covering all aspects of writing and publishing.",
    features: [
      "12 modules with video lessons",
      "Interactive writing assignments",
      "Community forum access",
      "Monthly live Q&A sessions",
      "Lifetime course access",
    ],
    icon: Video,
    price: "$297",
    duration: "one-time",
    ctaText: "Enroll Now",
  },
  {
    id: "6",
    title: "Writer's Community",
    description:
      "Join a supportive community of writers for ongoing motivation and feedback.",
    features: [
      "Private Facebook group",
      "Weekly writing prompts",
      "Monthly virtual meetups",
      "Accountability partnerships",
      "Resource library access",
    ],
    icon: MessageCircle,
    price: "$29",
    duration: "per month",
    ctaText: "Join Community",
  },
];

const Services = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <section id="services" ref={ref} className="py-20 bg-brand-neutral/30">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Users className="w-4 h-4" />
              Writing Services
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              How I Can Help You Succeed
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              Whether you're just starting your writing journey or looking to
              take your published works to the next level, I offer comprehensive
              support tailored to your specific needs and goals.
            </p>
          </div>

          {/* Services Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => {
              const IconComponent = service.icon;

              return (
                <animated.div
                  key={service.id}
                  style={{
                    opacity: inView ? 1 : 0,
                    transform: inView ? "translateY(0px)" : "translateY(50px)",
                    transitionDelay: inView ? `${200 + index * 100}ms` : "0ms",
                    transition: "all 0.6s ease-out",
                  }}
                >
                  <Card
                    className={`group h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 interactive-lift relative ${
                      service.popular ? "ring-2 ring-brand-accent/20" : ""
                    }`}
                  >
                    {service.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-brand-accent text-brand-primary px-4 py-1 rounded-full text-xs font-medium">
                          Most Popular
                        </span>
                      </div>
                    )}

                    <CardHeader className="text-center">
                      <div className="mx-auto w-16 h-16 bg-brand-accent/10 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-brand-accent/20 transition-colors">
                        <IconComponent className="w-8 h-8 text-brand-accent" />
                      </div>
                      <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                        {service.title}
                      </h3>
                      <p className="text-brand-secondary/70 leading-relaxed">
                        {service.description}
                      </p>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <div className="space-y-6">
                        {/* Features */}
                        <div className="space-y-3">
                          {service.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-brand-accent rounded-full mt-2 flex-shrink-0"></div>
                              <span className="text-sm text-brand-secondary/80">
                                {feature}
                              </span>
                            </div>
                          ))}
                        </div>

                        {/* Pricing */}
                        <div className="border-t border-brand-grey/30 pt-6">
                          <div className="text-center mb-4">
                            <div className="text-2xl font-bold text-brand-secondary">
                              {service.price}
                            </div>
                            <div className="text-sm text-brand-secondary/60">
                              {service.duration}
                            </div>
                          </div>

                          <Button
                            variant={service.popular ? "hero" : "outline"}
                            className="w-full group"
                            onClick={scrollToContact}
                          >
                            {service.ctaText}
                            <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </animated.div>
              );
            })}
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16">
            <div className="bg-brand-primary rounded-2xl p-8 shadow-elegant max-w-2xl mx-auto">
              <h3 className="text-2xl font-serif font-bold text-brand-secondary mb-4">
                Not sure which service is right for you?
              </h3>
              <p className="text-brand-secondary/70 mb-6">
                Schedule a free 15-minute consultation to discuss your writing
                goals and find the perfect fit.
              </p>
              <Button
                variant="hero"
                size="lg"
                onClick={scrollToContact}
                className="group"
              >
                Book Free Consultation
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default Services;
