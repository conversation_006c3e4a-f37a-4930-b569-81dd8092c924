import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ExternalLink,
  BookOpen,
  Users,
  Award,
  Calendar,
  Star,
  Quote,
} from "lucide-react";
import booksCollection from "@/assets/books-collection.jpg";

interface Work {
  id: string;
  title: string;
  category: "book" | "coaching" | "workshop" | "article";
  description: string;
  image: string;
  date: string;
  tags: string[];
  link?: string;
  stats?: {
    label: string;
    value: string;
  }[];
  testimonial?: {
    text: string;
    author: string;
    role: string;
  };
}

const works: Work[] = [
  {
    id: "1",
    title: "The Writer's Journey",
    category: "book",
    description:
      "A comprehensive guide helping over 10,000 writers craft compelling narratives that captivate readers from start to finish.",
    image: booksCollection,
    date: "2023",
    tags: ["Best Seller", "Writing Craft"],
    link: "https://amazon.com/writers-journey",
    stats: [
      { label: "Copies Sold", value: "10,000+" },
      { label: "Rating", value: "4.8/5" },
      { label: "Reviews", value: "250+" },
    ],
    testimonial: {
      text: "This book completely transformed my approach to storytelling.",
      author: "Michael Chen",
      role: "Published Author",
    },
  },
  {
    id: "2",
    title: "Advanced Writing Workshop Series",
    category: "workshop",
    description:
      "Six-month intensive program that guided 45 writers from first draft to published work.",
    image: booksCollection,
    date: "2023",
    tags: ["Group Coaching", "Advanced"],
    stats: [
      { label: "Participants", value: "45" },
      { label: "Published Works", value: "23" },
      { label: "Success Rate", value: "51%" },
    ],
    testimonial: {
      text: "The workshop structure and feedback were incredible. I finally finished my novel!",
      author: "Emma Rodriguez",
      role: "Workshop Graduate",
    },
  },
  {
    id: "3",
    title: "One-on-One Success Stories",
    category: "coaching",
    description:
      "Personal coaching that has helped 200+ writers overcome blocks, develop their voice, and achieve their publishing goals.",
    image: booksCollection,
    date: "Ongoing",
    tags: ["Personal Coaching", "Success Stories"],
    stats: [
      { label: "Writers Coached", value: "200+" },
      { label: "Books Published", value: "85" },
      { label: "Satisfaction", value: "98%" },
    ],
    testimonial: {
      text: "Sarah helped me find my authentic voice and overcome years of writer's block.",
      author: "David Thompson",
      role: "Coaching Client",
    },
  },
  {
    id: "4",
    title: "Publishing Mastery",
    category: "book",
    description:
      "Industry insider secrets that helped hundreds navigate the modern publishing landscape successfully.",
    image: booksCollection,
    date: "2022",
    tags: ["Publishing", "Industry Guide"],
    link: "https://amazon.com/publishing-mastery",
    stats: [
      { label: "Copies Sold", value: "7,500+" },
      { label: "Rating", value: "4.9/5" },
      { label: "Success Stories", value: "150+" },
    ],
  },
  {
    id: "5",
    title: "Writer's Block Solutions",
    category: "article",
    description:
      "Featured article series in Writer's Digest that helped thousands overcome creative barriers.",
    image: booksCollection,
    date: "2023",
    tags: ["Featured Article", "Writer's Digest"],
    link: "https://writersdigest.com",
    stats: [
      { label: "Readers", value: "50,000+" },
      { label: "Shares", value: "2,500+" },
      { label: "Comments", value: "800+" },
    ],
  },
  {
    id: "6",
    title: "Creative Courage",
    category: "book",
    description:
      "Mindset transformation guide that empowers writers to overcome fear and embrace their authentic creative voice.",
    image: booksCollection,
    date: "2021",
    tags: ["Mindset", "Creativity"],
    link: "https://amazon.com/creative-courage",
    stats: [
      { label: "Copies Sold", value: "12,000+" },
      { label: "Rating", value: "4.7/5" },
      { label: "Transformations", value: "300+" },
    ],
  },
];

const categoryIcons = {
  book: BookOpen,
  coaching: Users,
  workshop: Award,
  article: Quote,
};

const categoryColors = {
  book: "bg-brand-accent/10 text-brand-accent",
  coaching: "bg-blue-100 text-blue-600",
  workshop: "bg-green-100 text-green-600",
  article: "bg-purple-100 text-purple-600",
};

const MyWorks = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  return (
    <section id="myworks" ref={ref} className="py-20 bg-brand-neutral/30">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Award className="w-4 h-4" />
              Portfolio & Impact
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Proven Results & Success Stories
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              Real impact through books, coaching, and workshops that have
              transformed thousands of writing careers.
            </p>
          </div>

          {/* Works Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {works.map((work, index) => {
              const CategoryIcon = categoryIcons[work.category];

              return (
                <animated.div
                  key={work.id}
                  style={{
                    opacity: inView ? 1 : 0,
                    transform: inView ? "translateY(0px)" : "translateY(50px)",
                    transitionDelay: inView ? `${200 + index * 100}ms` : "0ms",
                    transition: "all 0.6s ease-out",
                  }}
                >
                  <Card className="group h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 interactive-lift">
                    <CardHeader className="p-0">
                      <div className="aspect-[4/3] rounded-t-lg overflow-hidden relative">
                        <img
                          src={work.image}
                          alt={work.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                          loading="lazy"
                        />
                        <div className="absolute top-4 left-4">
                          <Badge className={categoryColors[work.category]}>
                            <CategoryIcon className="w-3 h-3 mr-1" />
                            {work.category.charAt(0).toUpperCase() +
                              work.category.slice(1)}
                          </Badge>
                        </div>
                        <div className="absolute top-4 right-4">
                          <div className="flex items-center gap-1 text-sm text-brand-secondary/60 bg-white/90 px-2 py-1 rounded">
                            <Calendar className="w-3 h-3" />
                            {work.date}
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Title & Description */}
                        <div>
                          <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                            {work.title}
                          </h3>
                          <p className="text-brand-secondary/80 leading-relaxed text-sm">
                            {work.description}
                          </p>
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-2">
                          {work.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-brand-accent/10 text-brand-accent text-xs font-medium rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        {/* Stats */}
                        {work.stats && (
                          <div className="grid grid-cols-3 gap-2 text-center">
                            {work.stats.map((stat, idx) => (
                              <div
                                key={idx}
                                className="bg-brand-neutral/50 rounded-lg p-2"
                              >
                                <div className="text-lg font-bold text-brand-accent">
                                  {stat.value}
                                </div>
                                <div className="text-xs text-brand-secondary/60">
                                  {stat.label}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Testimonial */}
                        {work.testimonial && (
                          <div className="bg-brand-primary border-l-4 border-brand-accent p-3 rounded">
                            <p className="text-sm text-brand-secondary/80 italic mb-2">
                              "{work.testimonial.text}"
                            </p>
                            <div className="text-xs text-brand-secondary/60">
                              - {work.testimonial.author},{" "}
                              {work.testimonial.role}
                            </div>
                          </div>
                        )}

                        {/* Action */}
                        <div className="pt-2">
                          {work.link ? (
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full group"
                              onClick={() =>
                                window.open(
                                  work.link,
                                  "_blank",
                                  "noopener,noreferrer"
                                )
                              }
                            >
                              <ExternalLink className="w-4 h-4 mr-1" />
                              View Work
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full"
                              disabled
                            >
                              Private Work
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </animated.div>
              );
            })}
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16">
            <div className="bg-brand-primary rounded-2xl p-8 shadow-elegant max-w-2xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Star className="w-6 h-6 text-brand-accent" />
                <Star className="w-6 h-6 text-brand-accent" />
                <Star className="w-6 h-6 text-brand-accent" />
                <Star className="w-6 h-6 text-brand-accent" />
                <Star className="w-6 h-6 text-brand-accent" />
              </div>
              <h3 className="text-2xl font-serif font-bold text-brand-secondary mb-4">
                Ready to Create Your Success Story?
              </h3>
              <p className="text-brand-secondary/70 mb-6">
                Join the hundreds of writers who have transformed their craft
                and achieved their publishing dreams.
              </p>
              <Button
                variant="hero"
                size="lg"
                onClick={() =>
                  document
                    .getElementById("contact")
                    ?.scrollIntoView({ behavior: "smooth" })
                }
                className="group"
              >
                Start Your Journey
                <ExternalLink className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default MyWorks;
