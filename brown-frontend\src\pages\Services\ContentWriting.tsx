import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  PenTool,
  Globe,
  TrendingUp,
  Target,
  Clock,
  CheckCircle,
  Star,
  ArrowRight,
  FileText,
  Search,
  Users,
  Zap,
  Award,
  BarChart3,
} from "lucide-react";

const ContentWriting = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [servicesRef, servicesInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [processRef, processInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title = "Content Writing Services - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const servicesSpring = useSpring({
    opacity: servicesInView ? 1 : 0,
    transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
  });

  const processSpring = useSpring({
    opacity: processInView ? 1 : 0,
    transform: processInView ? "translateY(0px)" : "translateY(50px)",
  });

  const contentServices = [
    {
      title: "Blog Writing & Newsletter Content",
      description: "Engaging content that helps you share your message and establish your expertise in your field.",
      icon: PenTool,
      features: [
        "SEO-optimized blog posts",
        "Newsletter content creation",
        "Engaging headlines and hooks",
        "Message-focused writing",
        "Content calendar planning",
        "Brand voice consistency",
      ],
      price: "From $150 per post",
      popular: true,
    },
    {
      title: "Website Copy & Landing Pages",
      description: "Compelling website copy that converts visitors into customers and clients.",
      icon: Globe,
      features: [
        "Homepage and about page copy",
        "Service page descriptions",
        "Landing page optimization",
        "Sales page copywriting",
        "Email capture sequences",
        "Conversion rate optimization",
      ],
      price: "From $500 per page",
    },
    {
      title: "Email Marketing Campaigns",
      description: "Strategic email sequences that nurture leads and drive sales.",
      icon: Target,
      features: [
        "Welcome email sequences",
        "Newsletter content creation",
        "Product launch campaigns",
        "Nurture sequence development",
        "Subject line optimization",
        "A/B testing strategies",
      ],
      price: "From $200 per sequence",
    },
    {
      title: "Social Media Content",
      description: "Consistent, engaging content that builds your brand and community.",
      icon: Users,
      features: [
        "Platform-specific content",
        "Content calendar creation",
        "Hashtag research and strategy",
        "Engagement-focused posts",
        "Story and reel concepts",
        "Brand voice development",
      ],
      price: "From $300 per month",
    },
    {
      title: "White Papers & Case Studies",
      description: "In-depth content that showcases your expertise and builds authority.",
      icon: FileText,
      features: [
        "Research and data analysis",
        "Professional formatting",
        "Industry-specific insights",
        "Client success stories",
        "Lead generation focus",
        "Distribution strategy",
      ],
      price: "From $800 per piece",
    },
    {
      title: "Content Strategy & Consulting",
      description: "Comprehensive content planning that aligns with your business goals.",
      icon: BarChart3,
      features: [
        "Content audit and analysis",
        "Competitor research",
        "Content calendar development",
        "Brand voice guidelines",
        "Performance metrics setup",
        "Team training and support",
      ],
      price: "From $1,200 per month",
    },
  ];

  const contentProcess = [
    {
      step: "1",
      title: "Discovery & Strategy",
      description: "We dive deep into your brand, audience, and goals to create a tailored content strategy.",
      icon: Search,
    },
    {
      step: "2",
      title: "Research & Planning",
      description: "Thorough research on your industry, competitors, and target keywords to inform content creation.",
      icon: Target,
    },
    {
      step: "3",
      title: "Content Creation",
      description: "Crafting compelling, on-brand content that resonates with your audience and drives results.",
      icon: PenTool,
    },
    {
      step: "4",
      title: "Optimization & Delivery",
      description: "Final optimization for SEO and conversions, plus timely delivery in your preferred format.",
      icon: Zap,
    },
  ];

  const contentBenefits = [
    "Increased website traffic and engagement",
    "Higher search engine rankings",
    "Improved conversion rates",
    "Enhanced brand authority and credibility",
    "Consistent brand voice across all platforms",
    "Time savings for your team",
  ];

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="min-h-screen bg-cream-50 pt-16">
      {/* Hero Section */}
      <section ref={heroRef} className="py-20 bg-gradient-to-br from-brand-primary to-brand-neutral/20">
        <div className="container mx-auto px-4">
          <animated.div style={heroSpring} className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-6">
              <PenTool className="w-4 h-4" />
              Professional Content Writing Services
            </div>
            <h1 className="text-4xl md:text-6xl font-serif font-bold text-brand-secondary mb-6">
              Content Writing
            </h1>
            <p className="text-xl text-brand-secondary/70 mb-8 leading-relaxed">
              Writing is a change-provoking art. It's why we read. It's why we write. Strategic content writing that not only engages your audience but drives real business results. From blog posts to newsletters, I create content that helps you share your message clearly and compellingly.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="hero" size="lg" onClick={scrollToContact} className="group">
                Get Started Today
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                View Portfolio
              </Button>
            </div>
          </animated.div>
        </div>
      </section>

      {/* Services Section */}
      <section ref={servicesRef} className="py-20">
        <div className="container mx-auto px-4">
          <animated.div style={servicesSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Content Writing Services
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                Comprehensive content solutions designed to grow your business and connect with your audience.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {contentServices.map((service, index) => {
                const IconComponent = service.icon;
                return (
                  <animated.div
                    key={service.title}
                    style={{
                      opacity: servicesInView ? 1 : 0,
                      transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
                      transitionDelay: servicesInView ? `${200 + index * 100}ms` : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                  >
                    <Card className={`h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 relative ${service.popular ? "ring-2 ring-brand-accent/20" : ""
                      }`}>
                      {service.popular && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <span className="bg-brand-accent text-brand-primary px-4 py-1 rounded-full text-xs font-medium">
                            Most Popular
                          </span>
                        </div>
                      )}
                      <CardHeader>
                        <div className="flex items-center gap-4 mb-4">
                          <div className="w-12 h-12 bg-brand-accent/10 rounded-xl flex items-center justify-center">
                            <IconComponent className="w-6 h-6 text-brand-accent" />
                          </div>
                          <div>
                            <h3 className="text-lg font-serif font-bold text-brand-secondary">
                              {service.title}
                            </h3>
                            <p className="text-brand-accent font-medium text-sm">{service.price}</p>
                          </div>
                        </div>
                        <p className="text-brand-secondary/70 leading-relaxed text-sm">
                          {service.description}
                        </p>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2 mb-6">
                          {service.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start gap-2">
                              <CheckCircle className="w-3 h-3 text-brand-accent mt-1 flex-shrink-0" />
                              <span className="text-xs text-brand-secondary/80">{feature}</span>
                            </div>
                          ))}
                        </div>
                        <Button
                          variant={service.popular ? "hero" : "outline"}
                          className="w-full group text-sm"
                          onClick={scrollToContact}
                        >
                          Get Quote
                          <ArrowRight className="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform" />
                        </Button>
                      </CardContent>
                    </Card>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Process Section */}
      <section ref={processRef} className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <animated.div style={processSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                My Content Creation Process
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                A strategic approach that ensures every piece of content serves your business goals.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {contentProcess.map((step, index) => {
                const IconComponent = step.icon;
                return (
                  <animated.div
                    key={step.step}
                    style={{
                      opacity: processInView ? 1 : 0,
                      transform: processInView ? "translateY(0px)" : "translateY(50px)",
                      transitionDelay: processInView ? `${200 + index * 100}ms` : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                    className="text-center"
                  >
                    <div className="relative mb-6">
                      <div className="w-16 h-16 bg-brand-accent rounded-full flex items-center justify-center mx-auto mb-4">
                        <IconComponent className="w-8 h-8 text-brand-primary" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-brand-secondary text-brand-primary rounded-full flex items-center justify-center text-sm font-bold">
                        {step.step}
                      </div>
                    </div>
                    <h3 className="text-lg font-serif font-bold text-brand-secondary mb-3">
                      {step.title}
                    </h3>
                    <p className="text-brand-secondary/70 text-sm leading-relaxed">
                      {step.description}
                    </p>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Why Invest in Professional Content?
              </h2>
              <p className="text-lg text-brand-secondary/70">
                Quality content is an investment that pays dividends in brand growth and customer engagement.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              {contentBenefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-4 p-4 bg-brand-primary rounded-lg">
                  <div className="w-8 h-8 bg-brand-accent/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <CheckCircle className="w-4 h-4 text-brand-accent" />
                  </div>
                  <span className="text-brand-secondary font-medium">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Philosophy Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-8">
              Content That Changes Lives
            </h2>
            <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant">
              <p className="text-lg text-brand-secondary/80 leading-relaxed mb-6 italic">
                "Writing is a change-provoking art. It's why we read. It's why we write."
              </p>
              <p className="text-lg text-brand-secondary/80 leading-relaxed">
                Every piece of content is an opportunity to share your message and provoke positive change in your readers' lives. Whether it's a blog post, newsletter, or website copy, I help you create content that not only informs but transforms.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant max-w-4xl mx-auto text-center">
            <h3 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-6">
              Ready to Share Your Message?
            </h3>
            <p className="text-lg text-brand-secondary/70 mb-8 max-w-2xl mx-auto">
              Writing is a change-provoking art. It's why we read. It's why we write. Let's create content that not only tells your story but drives real results for your business and helps you share your message clearly and compellingly.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="hero" size="lg" onClick={scrollToContact} className="group">
                Start Your Project
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                Content Audit
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContentWriting;
