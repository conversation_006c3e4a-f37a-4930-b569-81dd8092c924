import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { ArrowRight, BookOpen, Users } from "lucide-react";
import heroIllustration from "@/assets/hero-illustration.jpg";

const Hero = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const heroSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
    delay: 200,
  });

  const imageSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView
      ? "translateX(0px) scale(1)"
      : "translateX(50px) scale(0.9)",
    delay: 400,
  });

  const scrollToBooks = () => {
    document.getElementById("books")?.scrollIntoView({ behavior: "smooth" });
  };

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <section
      id="home"
      ref={ref}
      className="min-h-screen flex items-center justify-center pt-16 pb-12 hero-bg"
    >
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <animated.div style={heroSpring} className="lg:pr-8">
            <div className="space-y-6">
              {/* Badge */}
              <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium">
                <BookOpen className="w-4 h-4" />
                Published Author & Writing Coach
              </div>

              {/* Headline */}
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-brand-secondary leading-tight">
                Get the help you need to write your{" "}
                <span className="text-brand-accent">book,</span>
                <br />
                to share your message — clearly,{""}
                <span className="text-brand-accent"> compellingly.</span>
              </h1>

              {/* Subheading */}
              <p className="text-lg md:text-xl text-brand-secondary/80 leading-relaxed max-w-2xl">
                Coaching, resources, and a proven roadmap for aspiring and
                established authors. Transform your ideas into published works
                that make an impact.
              </p>

              {/* CTAs */}
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button
                  variant="hero"
                  size="xl"
                  onClick={scrollToBooks}
                  className="group"
                >
                  Explore Books
                  <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button
                  variant="hero-outline"
                  size="xl"
                  onClick={scrollToContact}
                  className="group"
                >
                  <Users className="w-5 h-5 mr-1" />
                  Get Coaching
                </Button>
              </div>

              {/* Social Proof */}
              <div className="flex items-center gap-8 pt-8 text-brand-secondary/70">
                <div className="text-center">
                  <div className="text-2xl font-bold text-brand-accent">5</div>
                  <div className="text-sm">Published Books</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-brand-accent">
                    200+
                  </div>
                  <div className="text-sm">Writers Coached</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-brand-accent">10</div>
                  <div className="text-sm">Years Experience</div>
                </div>
              </div>
            </div>
          </animated.div>

          {/* Hero Illustration */}
          <animated.div style={imageSpring} className="lg:pl-8">
            <div className="relative">
              <div className="aspect-[4/3] rounded-2xl overflow-hidden shadow-elegant">
                <img
                  src={heroIllustration}
                  alt="Elegant author workspace with writing implements and books"
                  className="w-full h-full object-cover"
                  loading="eager"
                />
              </div>
              {/* Floating elements for visual interest */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-brand-accent/10 rounded-full blur-xl animate-pulse"></div>
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-brand-accent/5 rounded-full blur-2xl animate-pulse delay-1000"></div>
            </div>
          </animated.div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
