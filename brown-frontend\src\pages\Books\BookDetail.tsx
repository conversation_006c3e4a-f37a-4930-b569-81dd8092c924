import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown, Share2, ArrowLeft } from "lucide-react";

// Mock data - replace with Firestore data
const mockBookData = {
  "finding-your-voice": {
    id: "1",
    title: "Finding Your Voice: A Writer's Journey",
    shortBlurb:
      "Discover the unique voice that makes your writing unforgettable.",
    extendedBlurb:
      "In this comprehensive guide, <PERSON> takes you on a transformative journey to discover and develop your unique writing voice. Through practical exercises, real-world examples, and proven techniques, you'll learn to write with authenticity and confidence. Whether you're a beginner or an experienced writer, this book will help you break through creative blocks and find the voice that makes your writing truly memorable.",
    coverUrl:
      "https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=600&h=800&fit=crop",
    publicationDate: "2023-06-15",
    tags: ["Writing Craft", "Self-Help"],
    isbn: "978-1234567890",
    formats: ["Hardcover", "Paperback", "eBook", "Audiobook"],
    buyLinks: [
      {
        platform: "Amazon",
        url: "https://amazon.com/book1",
        format: "All formats",
      },
      {
        platform: "Barnes & Noble",
        url: "https://bn.com/book1",
        format: "Hardcover, Paperback",
      },
    ],
    excerpt: `Chapter 1: The Journey Begins

Every writer's journey starts with a single word, a single sentence, a single story that demands to be told. But finding your unique voice—that distinctive way of expressing yourself that sets your writing apart—can feel like searching for a needle in a haystack.

I remember the first time I truly heard my own voice in my writing. It wasn't in a classroom or during a formal writing exercise. It was in a letter I wrote to an imaginary cousin in Vienna, sitting in my secondary school classroom, mentally transporting myself to a place I'd never been but desperately wanted to visit.

That's when I realized that voice isn't something you find—it's something you uncover. It's been there all along, waiting beneath the layers of doubt, comparison, and the voices of others that we've internalized over the years.`,
    relatedBooks: ["storytelling-mastery"],
  },
};

const BookDetail = () => {
  const { slug } = useParams();
  const [book, setBook] = useState(null);
  const [isExcerptOpen, setIsExcerptOpen] = useState(false);

  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [contentRef, contentInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const contentSpring = useSpring({
    opacity: contentInView ? 1 : 0,
    transform: contentInView ? "translateY(0px)" : "translateY(30px)",
  });

  useEffect(() => {
    // In real implementation, fetch from Firestore
    if (slug && mockBookData[slug]) {
      setBook(mockBookData[slug]);
    }
  }, [slug]);

  const handleBuyClick = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: book.title,
          text: book.shortBlurb,
          url: window.location.href,
        });
      } catch (err) {
        console.log("Error sharing:", err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  if (!book) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-serif text-brown-900 mb-4">
            Book not found
          </h1>
          <Button asChild variant="outline">
            <Link to="/books">← Back to Books</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Navigation */}
      <div className="py-4 px-4 sm:px-6 lg:px-8">
        <Button
          asChild
          variant="ghost"
          className="text-brown-700 hover:text-brown-900"
        >
          <Link to="/books">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Books
          </Link>
        </Button>
      </div>

      {/* Hero Section */}
      <animated.section
        ref={heroRef}
        style={heroSpring}
        className="py-12 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-start">
            <div className="order-2 lg:order-1">
              <div className="flex flex-wrap gap-2 mb-4">
                {book.tags.map((tag) => (
                  <Badge key={tag} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
              <h1 className="text-3xl md:text-5xl font-serif text-brown-900 mb-6">
                {book.title}
              </h1>
              <p className="text-lg text-brown-700 mb-6 leading-relaxed">
                {book.extendedBlurb}
              </p>
              <div className="flex flex-wrap gap-3 mb-6">
                {book.buyLinks.map((link, index) => (
                  <Button
                    key={index}
                    onClick={() => handleBuyClick(link.url)}
                    className="bg-brown-800 hover:bg-brown-900 text-white"
                  >
                    Buy on {link.platform}
                  </Button>
                ))}
                <Button
                  onClick={handleShare}
                  variant="outline"
                  className="border-brown-300 text-brown-700 hover:bg-brown-50"
                >
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
            <div className="order-1 lg:order-2">
              <div className="aspect-[3/4] max-w-md mx-auto lg:max-w-none">
                <img
                  src={book.coverUrl}
                  alt={`${book.title} cover`}
                  className="w-full h-full object-cover rounded-lg shadow-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </animated.section>

      {/* Content Section */}
      <animated.section
        ref={contentRef}
        style={contentSpring}
        className="py-16 px-4 sm:px-6 lg:px-8 bg-white"
      >
        <div className="max-w-4xl mx-auto">
          {/* Excerpt */}
          <div className="mb-12">
            <Collapsible open={isExcerptOpen} onOpenChange={setIsExcerptOpen}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between text-lg py-6"
                >
                  Read Excerpt
                  <ChevronDown
                    className={`w-5 h-5 transition-transform ${
                      isExcerptOpen ? "rotate-180" : ""
                    }`}
                  />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-6">
                <Card>
                  <CardContent className="p-8">
                    <div className="prose prose-lg max-w-none text-brown-800">
                      {book.excerpt.split("\n\n").map((paragraph, index) => (
                        <p key={index} className="mb-4">
                          {paragraph}
                        </p>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </CollapsibleContent>
            </Collapsible>
          </div>

          {/* Metadata */}
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-xl font-serif text-brown-900 mb-4">
                  Book Details
                </h3>
                <div className="space-y-2 text-brown-700">
                  <div>
                    <strong>ISBN:</strong> {book.isbn}
                  </div>
                  <div>
                    <strong>Publication Date:</strong>{" "}
                    {new Date(book.publicationDate).toLocaleDateString()}
                  </div>
                  <div>
                    <strong>Available Formats:</strong>{" "}
                    {book.formats.join(", ")}
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <h3 className="text-xl font-serif text-brown-900 mb-4">
                  Where to Buy
                </h3>
                <div className="space-y-3">
                  {book.buyLinks.map((link, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center"
                    >
                      <div>
                        <div className="font-medium text-brown-800">
                          {link.platform}
                        </div>
                        <div className="text-sm text-brown-600">
                          {link.format}
                        </div>
                      </div>
                      <Button
                        onClick={() => handleBuyClick(link.url)}
                        size="sm"
                        className="bg-brown-800 hover:bg-brown-900 text-white"
                      >
                        Buy Now
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </animated.section>
    </div>
  );
};

export default BookDetail;
